# OIDC SAML API 系统总览

## 项目概述

本项目在 `api` 目录下实现了一个完整的 RESTful API 系统，符合现代编码规范，使用 Python 和 PostgreSQL。该系统为 SAML2 SP + OIDC IdP 认证中间件提供了完整的管理接口。

## 技术架构

### 核心技术栈
- **Python 3.9+**: 编程语言
- **Flask**: Web 框架
- **SQLAlchemy**: ORM 框架
- **PostgreSQL**: 主数据库
- **Redis**: 缓存和会话存储
- **Marshmallow**: 数据验证和序列化
- **Poetry**: 依赖管理
- **Docker**: 容器化部署

### 架构设计原则
- **模块化设计**: 按功能划分模块，便于维护和扩展
- **RESTful API**: 遵循 REST 设计原则
- **数据验证**: 使用 Marshmallow 进行严格的数据验证
- **权限控制**: 基于角色的访问控制 (RBAC)
- **错误处理**: 统一的错误响应格式
- **分页支持**: 列表接口支持分页、排序和过滤
- **容器化**: 支持 Docker 部署

## 目录结构

```
api/
├── __init__.py              # 应用工厂
├── app.py                   # 应用入口
├── config.py                # 配置管理
├── Dockerfile               # Docker 镜像构建
├── docker-compose.yml       # Docker Compose 配置
├── start.sh                 # 启动脚本
├── test_api.py              # API 测试脚本
├── README.md                # API 文档
├── models/                  # 数据模型
│   ├── __init__.py
│   ├── user.py              # 用户模型
│   └── oauth2.py            # OAuth2 相关模型
├── routes/                  # 路由处理
│   ├── __init__.py
│   ├── health.py            # 健康检查和系统信息
│   ├── users.py             # 用户管理
│   ├── oauth.py             # OAuth2 管理
│   └── auth.py              # 认证相关
├── utils/                   # 工具函数
│   ├── __init__.py
│   ├── auth.py              # 认证和授权
│   ├── pagination.py        # 分页处理
│   ├── response.py          # 响应格式化
│   ├── validation.py        # 数据验证
│   └── security.py          # 安全相关
├── migrations/              # 数据库迁移
│   ├── __init__.py
│   └── init_db.py           # 数据库初始化脚本
├── schemas.py               # 数据验证模式
├── utils.py                 # 工具函数 (已废弃，保留兼容性)
└── views.py                 # 视图函数 (已废弃，保留兼容性)
```

## 核心功能模块

### 1. 用户管理 (`routes/users.py`)
- 用户 CRUD 操作
- 用户权限管理
- 用户状态管理 (激活/停用)
- 支持分页、排序和过滤

### 2. OAuth2 管理 (`routes/oauth.py`)
- OAuth2 客户端管理
- 访问令牌管理
- 授权码管理
- 客户端密钥重新生成

### 3. 认证服务 (`routes/auth.py`)
- 认证状态查询
- 令牌验证
- 令牌撤销
- 权限信息查询

### 4. 系统监控 (`routes/health.py`)
- 健康检查
- 系统信息
- 统计数据
- 配置信息

## 数据模型

### 用户模型 (`models/user.py`)
```python
class User(db.Model):
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=True)
    saml_subject_id = Column(String(255), unique=True, nullable=True)
    saml_attributes = Column(JSONB, nullable=True)
    is_active = Column(Boolean, default=True)
    is_admin = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime, nullable=True)
```

### OAuth2 模型 (`models/oauth2.py`)
- `OAuth2Client`: OAuth2 客户端信息
- `OAuth2Token`: 访问令牌和刷新令牌
- `OAuth2AuthorizationCode`: 授权码

## API 端点

### 系统端点
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/info` - 系统信息
- `GET /api/v1/stats` - 统计信息
- `GET /api/v1/config` - 配置信息

### 用户管理
- `GET /api/v1/users` - 用户列表
- `POST /api/v1/users` - 创建用户
- `GET /api/v1/users/{id}` - 用户详情
- `PUT /api/v1/users/{id}` - 更新用户
- `DELETE /api/v1/users/{id}` - 删除用户

### OAuth2 管理
- `GET /api/v1/oauth/clients` - 客户端列表
- `POST /api/v1/oauth/clients` - 创建客户端
- `GET /api/v1/oauth/tokens` - 令牌列表
- `POST /api/v1/oauth/tokens/{id}/revoke` - 撤销令牌

### 认证服务
- `GET /api/v1/auth/status` - 认证状态
- `POST /api/v1/auth/validate-token` - 验证令牌
- `GET /api/v1/auth/permissions` - 权限信息

## 安装和运行

### 快速开始
```bash
# 进入 API 目录
cd api

# 使用启动脚本安装依赖
./start.sh install

# 初始化数据库
./start.sh init-db

# 启动应用
./start.sh start
```

### Docker 部署
```bash
# 使用 Docker Compose 启动
./start.sh docker

# 或者手动启动
docker-compose up -d
```

### 手动安装
```bash
# 安装 Poetry
curl -sSL https://install.python-poetry.org | python3 -

# 安装依赖
poetry install

# 配置数据库
cp ../config/config.yaml.example ../config/config.yaml
# 编辑配置文件

# 初始化数据库
poetry run python migrations/init_db.py init

# 启动应用
poetry run python app.py
```

## 配置管理

配置文件位于 `../config/config.yaml`:
```yaml
database:
  url: "postgresql://username:password@localhost:5432/oidc_db"

redis:
  host: "localhost"
  port: 6379
  db: 0

saml:
  entity_id: "urn:example:sp"
  # ... 其他配置

oidc:
  issuer: "https://sp.example.com"
  # ... 其他配置
```

## 认证和授权

### 认证方式
1. **会话认证**: 通过 SAML 登录建立的会话
2. **OAuth2 Bearer Token**: 在请求头中包含访问令牌

### 权限控制
- **普通用户**: 可以查看和修改自己的信息
- **管理员**: 可以管理所有用户和 OAuth2 客户端

### 装饰器
- `@require_auth`: 需要认证
- `@require_admin`: 需要管理员权限
- `@require_self_or_admin`: 需要是用户本人或管理员

## 数据验证

使用 Marshmallow 进行数据验证:
```python
class UserCreateSchema(Schema):
    username = fields.String(required=True, validate=validate.Length(min=3, max=80))
    email = fields.Email(allow_none=True)
    is_admin = fields.Boolean(missing=False)
```

## 错误处理

统一的错误响应格式:
```json
{
  "success": false,
  "error": "error_type",
  "message": "错误描述",
  "details": {}
}
```

## 测试

```bash
# 运行 API 测试
./start.sh test

# 或者直接运行
python test_api.py
```

## 部署建议

### 开发环境
- 使用 `./start.sh start` 启动
- 使用 SQLite 或本地 PostgreSQL
- 启用调试模式

### 生产环境
- 使用 Docker 部署
- 使用 PostgreSQL 集群
- 使用 Redis 集群
- 使用 Nginx 作为反向代理
- 使用 Gunicorn 作为 WSGI 服务器

## 扩展建议

1. **添加更多认证方式**: LDAP、OAuth2 等
2. **实现更细粒度的权限控制**: 基于资源的权限
3. **添加审计日志**: 记录所有操作
4. **实现 API 限流**: 防止滥用
5. **添加监控和告警**: Prometheus + Grafana
6. **实现 API 文档**: Swagger/OpenAPI

## 总结

该 API 系统采用现代化的架构设计，具有以下特点:

- ✅ **模块化设计**: 易于维护和扩展
- ✅ **符合编码规范**: 遵循 Python 和 Flask 最佳实践
- ✅ **完整的功能**: 涵盖用户管理、OAuth2 管理和认证服务
- ✅ **数据验证**: 严格的输入验证和错误处理
- ✅ **权限控制**: 基于角色的访问控制
- ✅ **PostgreSQL 支持**: 使用现代化的数据库
- ✅ **容器化部署**: 支持 Docker 和 Docker Compose
- ✅ **完整文档**: 详细的 API 文档和使用说明

该系统可以作为 SAML2 SP + OIDC IdP 认证中间件的管理后台，提供完整的 API 接口供前端应用或其他系统调用。
