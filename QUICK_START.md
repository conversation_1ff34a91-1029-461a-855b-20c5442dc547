# 快速启动指南

## 数据库配置已完成

系统已配置为使用您提供的 PostgreSQL 数据库：

- **主机**: ***********
- **端口**: 5432
- **数据库**: openauth
- **用户**: root
- **密码**: r3j94pf2c

## 快速启动步骤

### 1. 进入 API 目录
```bash
cd api
```

### 2. 验证配置
```bash
./start.sh verify
```
这将检查：
- 配置文件是否正确
- 依赖包是否安装
- 文件结构是否完整

### 3. 安装依赖（如果需要）
```bash
./start.sh install
```

### 4. 测试数据库连接
```bash
./start.sh test-db
```
这将测试：
- psycopg2 连接
- SQLAlchemy 连接
- Flask 应用连接
- 数据库权限

### 5. 初始化数据库
```bash
./start.sh init-db
```
这将：
- 创建所有数据表
- 创建默认管理员用户 (admin/<EMAIL>)
- 创建示例 OAuth2 客户端

### 6. 启动应用
```bash
./start.sh start
```

### 7. 测试 API
```bash
./start.sh test
```

## 验证安装

启动后，您可以访问以下端点验证系统是否正常工作：

- **健康检查**: http://localhost:5000/api/v1/health
- **系统信息**: http://localhost:5000/api/v1/info
- **统计信息**: http://localhost:5000/api/v1/stats

## 主要 API 端点

### 系统监控
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/info` - 系统信息
- `GET /api/v1/stats` - 统计信息
- `GET /api/v1/config` - 配置信息

### 用户管理
- `GET /api/v1/users` - 用户列表 (需要认证)
- `POST /api/v1/users` - 创建用户 (需要管理员权限)
- `GET /api/v1/users/me` - 当前用户信息 (需要认证)

### OAuth2 管理
- `GET /api/v1/oauth/clients` - 客户端列表 (需要管理员权限)
- `POST /api/v1/oauth/clients` - 创建客户端 (需要管理员权限)
- `GET /api/v1/oauth/tokens` - 令牌列表 (需要管理员权限)

### 认证服务
- `GET /api/v1/auth/status` - 认证状态
- `POST /api/v1/auth/validate-token` - 验证令牌
- `GET /api/v1/auth/permissions` - 权限信息

## 默认账户

初始化数据库后，系统会创建以下默认账户：

- **管理员用户**:
  - 用户名: admin
  - 邮箱: <EMAIL>
  - 权限: 管理员

- **示例 OAuth2 客户端**:
  - 客户端名称: Test Client
  - 重定向 URI: http://localhost:3000/callback
  - 作用域: openid profile email

## 使用 Docker 部署

如果您希望使用 Docker 部署（仅包含 Redis，使用外部数据库）：

```bash
./start.sh docker
```

这将启动：
- API 应用 (端口 5000)
- Redis 缓存 (端口 6379)

## 故障排除

### 1. 数据库连接失败
```bash
# 测试数据库连接
./start.sh test-db

# 检查网络连接
ping ***********

# 检查端口是否开放
telnet *********** 5432
```

### 2. 依赖包安装失败
```bash
# 使用 Poetry 安装
poetry install

# 或使用 pip 安装
pip install psycopg2-binary flask sqlalchemy marshmallow flask-cors
```

### 3. 权限问题
确保数据库用户 `root` 具有以下权限：
- 创建表
- 插入、更新、删除数据
- 创建索引

### 4. 端口冲突
如果端口 5000 被占用，可以设置环境变量：
```bash
export PORT=8000
./start.sh start
```

## 配置文件

主要配置文件位于 `../config/config.yaml`：

```yaml
database:
  url: "********************************************/openauth"

redis:
  host: "localhost"
  port: 6379
  db: 0

saml:
  entity_id: "urn:example:sp"
  acs_url: "https://sp.example.com/saml/acs"
  # ... 其他 SAML 配置

oidc:
  issuer: "https://sp.example.com"
  access_token_exp: 3600
  id_token_exp: 3600
  # ... 其他 OIDC 配置
```

## 环境变量

可以通过环境变量覆盖配置：

```bash
export DATABASE_URL="********************************************/openauth"
export SECRET_KEY="your-secret-key"
export FLASK_ENV="development"
export REDIS_HOST="localhost"
```

## 下一步

1. **集成 SAML**: 配置 SAML SP 与外部 IdP 集成
2. **配置 OIDC**: 设置 OIDC IdP 参数
3. **添加客户端**: 创建 OAuth2 客户端应用
4. **用户管理**: 通过 API 管理用户账户
5. **监控部署**: 设置生产环境监控

## 支持

如果遇到问题，请检查：
1. 日志输出中的错误信息
2. 数据库连接是否正常
3. 依赖包是否完整安装
4. 配置文件是否正确

更多详细信息请参考 `api/README.md` 文档。
