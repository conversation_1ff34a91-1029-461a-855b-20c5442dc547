# SAML2 SP + OIDC IdP 项目

这是一个同时实现 SAML2 服务提供者 (SP) 和 OpenID Connect 身份提供者 (IdP) 的认证中间件系统。

## 功能特性

- SAML2 服务提供者 (SP) - 与外部 SAML 身份提供者交互
- OpenID Connect 身份提供者 (IdP) - 向内部应用提供 OIDC 认证服务
- 用户会话管理
- OAuth2 授权码流程支持
- 可扩展的架构设计

## 技术栈

- Flask 2.0+ 作为 Web 框架
- PySAML2 7.0+ 实现 SAML2 SP 功能
- Authlib 1.6+ 实现 OIDC IdP 功能
- SQLAlchemy 1.4+ 作为 ORM
- Redis 4.5+ 用于缓存和会话管理
- PyYAML 5.4+ 用于配置管理

## 目录结构

```
oidc/
├── app/                        # Flask 应用主目录
│   ├── __init__.py            # 应用初始化
│   ├── models/                # 数据模型
│   ├── saml/                  # SAML SP 模块
│   ├── oidc/                  # OIDC IdP 模块
│   ├── auth/                  # 认证管理模块
│   └── templates/             # HTML 模板
├── config/                    # 配置文件
├── migrations/                # 数据库迁移脚本
├── tests/                     # 测试代码
├── certs/                     # 证书文件
├── static/                    # 静态资源
├── requirements.txt           # 依赖包列表
├── run.py                     # 应用入口
└── README.md                  # 项目说明文档
```

## 安装与运行

1. 创建虚拟环境:
   ```
   python3 -m venv venv
   source venv/bin/activate  # Linux/Mac
   # 或
   venv\Scripts\activate     # Windows
   ```

2. 安装依赖:
   ```
   pip install -r requirements.txt
   ```

3. 运行应用:
   ```
   python run.py
   ```

## 配置

项目配置位于 `config/config.yaml` 文件中，请根据实际环境修改配置。

## 许可证

[待定]