# OIDC SAML API 系统

基于 Flask 的 RESTful API 系统，提供用户管理、OAuth2 客户端管理和认证服务。

## 功能特性

- **用户管理**: 完整的用户 CRUD 操作
- **OAuth2 客户端管理**: OAuth2 客户端的创建、更新、删除
- **令牌管理**: OAuth2 访问令牌和刷新令牌管理
- **认证授权**: 基于会话和 OAuth2 令牌的认证
- **权限控制**: 基于角色的访问控制
- **数据验证**: 使用 Marshmallow 进行数据验证
- **分页支持**: 列表接口支持分页、排序和过滤
- **PostgreSQL 支持**: 使用 PostgreSQL 作为主数据库
- **健康检查**: 系统健康状态监控

## 技术栈

- **Flask**: Web 框架
- **SQLAlchemy**: ORM
- **PostgreSQL**: 数据库
- **Marshmallow**: 数据验证和序列化
- **Flask-CORS**: 跨域支持
- **Poetry**: 依赖管理

## 安装和运行

### 使用 Poetry (推荐)

1. 安装 Poetry:
```bash
curl -sSL https://install.python-poetry.org | python3 -
```

2. 安装依赖:
```bash
cd api
poetry install
```

3. 配置数据库:
```bash
# 编辑配置文件
cp ../config/config.yaml.example ../config/config.yaml
# 修改数据库连接信息
```

4. 初始化数据库:
```bash
poetry run python migrations/init_db.py init
```

5. 运行应用:
```bash
poetry run python app.py
```

### 传统方式

1. 创建虚拟环境:
```bash
python3 -m venv venv
source venv/bin/activate
```

2. 安装依赖:
```bash
pip install -r requirements.txt
```

3. 运行应用:
```bash
python app.py
```

## API 端点

### 健康检查和系统信息

- `GET /api/v1/health` - 健康检查
- `GET /api/v1/info` - 系统信息
- `GET /api/v1/stats` - 统计信息
- `GET /api/v1/config` - 公开配置信息

### 用户管理

- `GET /api/v1/users` - 获取用户列表
- `GET /api/v1/users/{id}` - 获取用户详情
- `POST /api/v1/users` - 创建用户 (管理员)
- `PUT /api/v1/users/{id}` - 更新用户
- `DELETE /api/v1/users/{id}` - 删除用户 (管理员)
- `GET /api/v1/users/me` - 获取当前用户信息
- `PUT /api/v1/users/me` - 更新当前用户信息

### OAuth2 客户端管理

- `GET /api/v1/oauth/clients` - 获取客户端列表 (管理员)
- `GET /api/v1/oauth/clients/{id}` - 获取客户端详情 (管理员)
- `POST /api/v1/oauth/clients` - 创建客户端 (管理员)
- `PUT /api/v1/oauth/clients/{id}` - 更新客户端 (管理员)
- `DELETE /api/v1/oauth/clients/{id}` - 删除客户端 (管理员)
- `POST /api/v1/oauth/clients/{id}/regenerate-secret` - 重新生成密钥 (管理员)

### OAuth2 令牌管理

- `GET /api/v1/oauth/tokens` - 获取令牌列表 (管理员)
- `GET /api/v1/oauth/tokens/{id}` - 获取令牌详情 (管理员)
- `POST /api/v1/oauth/tokens/{id}/revoke` - 撤销令牌 (管理员)
- `GET /api/v1/oauth/tokens/user/{user_id}` - 获取用户令牌 (管理员)
- `GET /api/v1/oauth/tokens/client/{client_id}` - 获取客户端令牌 (管理员)

### 认证相关

- `GET /api/v1/auth/status` - 获取认证状态
- `POST /api/v1/auth/logout` - 登出
- `POST /api/v1/auth/validate-token` - 验证令牌
- `GET /api/v1/auth/token-info` - 获取令牌信息
- `POST /api/v1/auth/revoke-token` - 撤销当前令牌
- `GET /api/v1/auth/user-tokens` - 获取用户令牌列表
- `POST /api/v1/auth/revoke-all-tokens` - 撤销所有令牌
- `GET /api/v1/auth/permissions` - 获取权限信息

## 认证方式

### 1. 会话认证
通过 SAML 登录后建立的会话。

### 2. OAuth2 Bearer Token
在请求头中包含访问令牌:
```
Authorization: Bearer <access_token>
```

## 权限控制

### 用户角色
- **普通用户**: 可以查看和修改自己的信息
- **管理员**: 可以管理所有用户和 OAuth2 客户端

### 权限检查
- `@require_auth`: 需要认证
- `@require_admin`: 需要管理员权限
- `@require_self_or_admin`: 需要是用户本人或管理员

## 数据验证

使用 Marshmallow 进行数据验证，支持:
- 字段类型验证
- 长度验证
- 格式验证 (邮箱、URL 等)
- 自定义验证规则

## 分页和过滤

列表接口支持以下查询参数:
- `page`: 页码 (默认: 1)
- `per_page`: 每页数量 (默认: 20, 最大: 100)
- `sort`: 排序字段
- `order`: 排序方向 (asc/desc)
- 其他字段可用于过滤

## 错误处理

统一的错误响应格式:
```json
{
  "success": false,
  "error": "error_type",
  "message": "错误描述",
  "details": {}
}
```

## 成功响应

统一的成功响应格式:
```json
{
  "success": true,
  "message": "操作成功",
  "data": {}
}
```

## 配置

配置文件位于 `../config/config.yaml`:
```yaml
database:
  url: "postgresql://username:password@localhost:5432/oidc_db"

redis:
  host: "localhost"
  port: 6379
  db: 0

saml:
  entity_id: "urn:example:sp"
  # ... 其他 SAML 配置

oidc:
  issuer: "https://sp.example.com"
  # ... 其他 OIDC 配置
```

## 开发

### 代码结构
```
api/
├── __init__.py          # 应用工厂
├── app.py              # 应用入口
├── config.py           # 配置管理
├── models/             # 数据模型
├── routes/             # 路由处理
├── utils/              # 工具函数
├── schemas.py          # 数据验证模式
└── migrations/         # 数据库迁移
```

### 添加新功能
1. 在 `models/` 中定义数据模型
2. 在 `schemas.py` 中定义验证模式
3. 在 `routes/` 中实现路由处理
4. 在 `routes/__init__.py` 中注册蓝图

## 测试

```bash
# 运行测试
poetry run pytest

# 运行测试并生成覆盖率报告
poetry run pytest --cov=api
```

## 部署

### Docker 部署
```bash
# 构建镜像
docker build -t oidc-api .

# 运行容器
docker run -p 5000:5000 -e DATABASE_URL=postgresql://... oidc-api
```

### 生产环境
建议使用 Gunicorn 作为 WSGI 服务器:
```bash
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```
