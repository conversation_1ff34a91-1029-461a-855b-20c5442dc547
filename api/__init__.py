"""
API 模块初始化文件
提供 RESTful API 接口
"""
from flask import Blueprint

# 创建 API 蓝图
api_bp = Blueprint('api', __name__)

# 导入视图模块
from . import views
from . import auth_views
from . import user_views
from . import oauth_views

def init_app(app):
    """
    初始化 API 模块
    """
    # 注册 API 蓝图
    app.register_blueprint(api_bp, url_prefix='/api/v1')
    
    # 配置 CORS
    try:
        from flask_cors import CORS
        CORS(app, resources={
            r"/api/*": {
                "origins": "*",
                "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                "allow_headers": ["Content-Type", "Authorization"]
            }
        })
    except ImportError:
        pass
