"""
API 应用程序包
基于 Flask 的 RESTful API 服务
"""
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_cors import CORS
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 初始化扩展
db = SQLAlchemy()
migrate = Migrate()

def create_app(config_name='default'):
    """
    应用工厂函数
    """
    app = Flask(__name__)

    # 加载配置
    if config_name == 'default':
        # 尝试加载 YAML 配置文件
        try:
            from config import load_config
            config = load_config()
            app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key')
            app.config['SQLALCHEMY_DATABASE_URI'] = config.get('database', {}).get('url', '********************************************/openauth')
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            app.config['SECRET_KEY'] = 'dev-secret-key'
            app.config['SQLALCHEMY_DATABASE_URI'] = '********************************************/openauth'
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)

    # 配置 CORS
    CORS(app, resources={
        r"/api/*": {
            "origins": "*",
            "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": ["Content-Type", "Authorization"]
        }
    })

    # 注册蓝图
    from .routes import register_blueprints
    register_blueprints(app)

    # 创建数据库表
    with app.app_context():
        db.create_all()

    @app.route('/')
    def index():
        return {'message': 'API Server is running', 'version': '1.0.0'}

    return app
