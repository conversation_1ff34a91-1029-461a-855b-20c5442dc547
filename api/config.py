"""
API 配置管理
"""
import os
import yaml
from pathlib import Path

def load_config(config_path=None):
    """
    加载配置文件
    
    Args:
        config_path (str, optional): 配置文件路径
    
    Returns:
        dict: 配置字典
    """
    if config_path is None:
        # 默认配置文件路径
        config_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'config',
            'config.yaml'
        )
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        print(f"配置文件未找到: {config_path}")
        return get_default_config()
    except yaml.YAMLError as e:
        print(f"配置文件解析错误: {e}")
        return get_default_config()

def get_default_config():
    """
    获取默认配置
    
    Returns:
        dict: 默认配置字典
    """
    return {
        'database': {
            'url': os.environ.get('DATABASE_URL', 'postgresql://username:password@localhost:5432/oidc_db')
        },
        'redis': {
            'host': os.environ.get('REDIS_HOST', 'localhost'),
            'port': int(os.environ.get('REDIS_PORT', 6379)),
            'db': int(os.environ.get('REDIS_DB', 0))
        },
        'saml': {
            'entity_id': os.environ.get('SAML_ENTITY_ID', 'urn:example:sp'),
            'acs_url': os.environ.get('SAML_ACS_URL', 'https://sp.example.com/saml/acs'),
            'sp_cert': os.environ.get('SAML_SP_CERT', 'certs/sp.crt'),
            'sp_key': os.environ.get('SAML_SP_KEY', 'certs/sp.key'),
            'idp_metadata_url': os.environ.get('SAML_IDP_METADATA_URL', 'https://idp.example.com/metadata.xml'),
            'idp_entity_id': os.environ.get('SAML_IDP_ENTITY_ID', 'https://idp.example.com/metadata')
        },
        'oidc': {
            'issuer': os.environ.get('OIDC_ISSUER', 'https://sp.example.com'),
            'jwks': {
                'private_key': os.environ.get('OIDC_PRIVATE_KEY', 'certs/private.key'),
                'public_key': os.environ.get('OIDC_PUBLIC_KEY', 'certs/public.pem')
            },
            'access_token_exp': int(os.environ.get('OIDC_ACCESS_TOKEN_EXP', 3600)),
            'id_token_exp': int(os.environ.get('OIDC_ID_TOKEN_EXP', 3600))
        }
    }

def get_database_url():
    """
    获取数据库连接 URL
    
    Returns:
        str: 数据库连接 URL
    """
    config = load_config()
    return config.get('database', {}).get('url', 'postgresql://username:password@localhost:5432/oidc_db')

def get_redis_config():
    """
    获取 Redis 配置
    
    Returns:
        dict: Redis 配置字典
    """
    config = load_config()
    return config.get('redis', {
        'host': 'localhost',
        'port': 6379,
        'db': 0
    })

def get_saml_config():
    """
    获取 SAML 配置
    
    Returns:
        dict: SAML 配置字典
    """
    config = load_config()
    return config.get('saml', {})

def get_oidc_config():
    """
    获取 OIDC 配置
    
    Returns:
        dict: OIDC 配置字典
    """
    config = load_config()
    return config.get('oidc', {})

class Config:
    """
    配置类
    """
    def __init__(self, config_path=None):
        self.config = load_config(config_path)
    
    @property
    def database_url(self):
        return self.config.get('database', {}).get('url')
    
    @property
    def redis_config(self):
        return self.config.get('redis', {})
    
    @property
    def saml_config(self):
        return self.config.get('saml', {})
    
    @property
    def oidc_config(self):
        return self.config.get('oidc', {})
    
    def get(self, key, default=None):
        """
        获取配置值
        
        Args:
            key (str): 配置键，支持点号分隔的嵌套键
            default: 默认值
        
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value

# 全局配置实例
config = Config()
