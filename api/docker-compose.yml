version: '3.8'

services:
  # PostgreSQL 数据库 (注释掉，使用外部数据库)
  # postgres:
  #   image: postgres:13
  #   container_name: oidc_postgres
  #   environment:
  #     POSTGRES_DB: openauth
  #     POSTGRES_USER: root
  #     POSTGRES_PASSWORD: r3j94pf2c
  #   ports:
  #     - "5432:5432"
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - oidc_network
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U root -d openauth"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: oidc_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - oidc_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API 应用
  api:
    build: .
    container_name: oidc_api
    ports:
      - "5000:5000"
    environment:
      - DATABASE_URL=********************************************/openauth
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - SECRET_KEY=your-secret-key-here
      - FLASK_ENV=development
    volumes:
      - .:/app
      - ../config:/app/config
    networks:
      - oidc_network
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # pgAdmin (可选，用于数据库管理) - 注释掉，使用外部数据库
  # pgadmin:
  #   image: dpage/pgadmin4:latest
  #   container_name: oidc_pgadmin
  #   environment:
  #     PGADMIN_DEFAULT_EMAIL: <EMAIL>
  #     PGADMIN_DEFAULT_PASSWORD: admin
  #   ports:
  #     - "8080:80"
  #   volumes:
  #     - pgadmin_data:/var/lib/pgadmin
  #   networks:
  #     - oidc_network

volumes:
  postgres_data:
  redis_data:
  pgadmin_data:

networks:
  oidc_network:
    driver: bridge
