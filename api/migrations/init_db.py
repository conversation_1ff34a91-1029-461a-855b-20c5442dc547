"""
数据库初始化脚本
"""
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from api import create_app, db
from api.models import User, OAuth2Client, OAuth2Token, OAuth2AuthorizationCode

def init_database():
    """
    初始化数据库
    """
    app = create_app()
    
    with app.app_context():
        # 创建所有表
        db.create_all()
        print("数据库表创建成功")
        
        # 创建默认管理员用户
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            admin_user = User.create_user(
                username='admin',
                email='<EMAIL>',
                is_admin=True
            )
            print(f"默认管理员用户创建成功: {admin_user.username}")
        else:
            print("管理员用户已存在")
        
        # 创建示例 OAuth2 客户端
        test_client = OAuth2Client.query.filter_by(client_name='Test Client').first()
        if not test_client:
            test_client = OAuth2Client.create_client(
                client_name='Test Client',
                redirect_uris=['http://localhost:3000/callback'],
                client_uri='http://localhost:3000',
                scope='openid profile email'
            )
            print(f"示例 OAuth2 客户端创建成功:")
            print(f"  Client ID: {test_client.client_id}")
            print(f"  Client Secret: {test_client.client_secret}")
        else:
            print("示例 OAuth2 客户端已存在")

def drop_database():
    """
    删除数据库
    """
    app = create_app()
    
    with app.app_context():
        db.drop_all()
        print("数据库表删除成功")

def reset_database():
    """
    重置数据库
    """
    print("重置数据库...")
    drop_database()
    init_database()
    print("数据库重置完成")

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='数据库管理脚本')
    parser.add_argument('action', choices=['init', 'drop', 'reset'], 
                       help='要执行的操作: init(初始化), drop(删除), reset(重置)')
    
    args = parser.parse_args()
    
    if args.action == 'init':
        init_database()
    elif args.action == 'drop':
        drop_database()
    elif args.action == 'reset':
        reset_database()
