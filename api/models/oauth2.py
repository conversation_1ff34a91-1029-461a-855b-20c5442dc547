"""
OAuth2 相关数据模型
"""
from api import db
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.mutable import MutableDict
from sqlalchemy.orm import relationship
from datetime import datetime
import secrets
import time

# 为兼容不同数据库，如果没有 PostgreSQL，则使用 JSON 类型
try:
    JSONType = JSONB
except:
    from sqlalchemy import JSON
    JSONType = JSON

class OAuth2Client(db.Model):
    """
    OAuth2 客户端模型
    """
    __tablename__ = 'oauth2_clients'
    
    id = Column(Integer, primary_key=True)
    client_id = Column(String(48), unique=True, nullable=False, index=True)
    client_secret = Column(String(120), nullable=False)
    client_name = Column(String(120), nullable=False)
    client_uri = Column(Text, nullable=True)
    logo_uri = Column(Text, nullable=True)
    contact = Column(Text, nullable=True)
    tos_uri = Column(Text, nullable=True)
    policy_uri = Column(Text, nullable=True)
    
    # JSON 字段
    redirect_uris = Column(MutableDict.as_mutable(JSONType), nullable=False)
    grant_types = Column(MutableDict.as_mutable(JSONType), default=['authorization_code'])
    response_types = Column(MutableDict.as_mutable(JSONType), default=['code'])
    
    # 认证方法
    token_endpoint_auth_method = Column(String(48), default='client_secret_basic')
    scope = Column(Text, default='openid')
    
    # 状态
    is_active = Column(Boolean, default=True, nullable=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # 关系
    tokens = relationship('OAuth2Token', backref='client', lazy='dynamic')
    authorization_codes = relationship('OAuth2AuthorizationCode', backref='client', lazy='dynamic')
    
    def __repr__(self):
        return f'<OAuth2Client {self.client_name}>'
    
    def check_client_secret(self, client_secret):
        """
        验证客户端密钥
        """
        return secrets.compare_digest(self.client_secret, client_secret)
    
    def check_redirect_uri(self, redirect_uri):
        """
        验证重定向 URI
        """
        return redirect_uri in self.redirect_uris
    
    def check_grant_type(self, grant_type):
        """
        验证授权类型
        """
        if self.grant_types is None:
            return True
        return grant_type in self.grant_types
    
    def check_response_type(self, response_type):
        """
        验证响应类型
        """
        if self.response_types is None:
            return True
        return response_type in self.response_types
    
    def to_dict(self, include_secret=False):
        """
        转换为字典格式
        """
        data = {
            'id': self.id,
            'client_id': self.client_id,
            'client_name': self.client_name,
            'client_uri': self.client_uri,
            'logo_uri': self.logo_uri,
            'contact': self.contact,
            'tos_uri': self.tos_uri,
            'policy_uri': self.policy_uri,
            'redirect_uris': self.redirect_uris,
            'grant_types': self.grant_types,
            'response_types': self.response_types,
            'token_endpoint_auth_method': self.token_endpoint_auth_method,
            'scope': self.scope,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        if include_secret:
            data['client_secret'] = self.client_secret
        
        return data
    
    @classmethod
    def find_by_client_id(cls, client_id):
        """
        根据客户端 ID 查找客户端
        """
        return cls.query.filter_by(client_id=client_id, is_active=True).first()
    
    @classmethod
    def create_client(cls, client_name, redirect_uris, **kwargs):
        """
        创建新的 OAuth2 客户端
        """
        client_id = secrets.token_urlsafe(32)
        client_secret = secrets.token_urlsafe(48)
        
        client = cls(
            client_id=client_id,
            client_secret=client_secret,
            client_name=client_name,
            redirect_uris=redirect_uris,
            **kwargs
        )
        db.session.add(client)
        db.session.commit()
        return client

class OAuth2AuthorizationCode(db.Model):
    """
    OAuth2 授权码模型
    """
    __tablename__ = 'oauth2_authorization_codes'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(120), unique=True, nullable=False, index=True)
    client_id = Column(String(48), ForeignKey('oauth2_clients.client_id'), nullable=False)
    redirect_uri = Column(Text, nullable=True)
    response_type = Column(Text, nullable=True)
    scope = Column(Text, nullable=True)
    auth_time = Column(Integer, nullable=False)
    
    # PKCE 支持
    code_challenge = Column(Text, nullable=True)
    code_challenge_method = Column(String(48), nullable=True)
    
    # OIDC 支持
    nonce = Column(Text, nullable=True)
    
    # 用户关联
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # 状态
    is_used = Column(Boolean, default=False, nullable=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # 关系
    user = relationship('User', backref='authorization_codes')
    
    def __repr__(self):
        return f'<OAuth2AuthorizationCode {self.code[:10]}...>'
    
    def is_expired(self, expires_in=600):
        """
        检查授权码是否过期（默认10分钟）
        """
        return (datetime.utcnow() - self.created_at).total_seconds() > expires_in
    
    def mark_as_used(self):
        """
        标记授权码为已使用
        """
        self.is_used = True
        db.session.commit()
    
    def to_dict(self):
        """
        转换为字典格式
        """
        return {
            'id': self.id,
            'code': self.code,
            'client_id': self.client_id,
            'redirect_uri': self.redirect_uri,
            'response_type': self.response_type,
            'scope': self.scope,
            'auth_time': self.auth_time,
            'user_id': self.user_id,
            'is_used': self.is_used,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def create_code(cls, client_id, user_id, redirect_uri=None, scope=None, **kwargs):
        """
        创建新的授权码
        """
        code = secrets.token_urlsafe(32)
        auth_time = int(time.time())
        
        authorization_code = cls(
            code=code,
            client_id=client_id,
            user_id=user_id,
            redirect_uri=redirect_uri,
            scope=scope,
            auth_time=auth_time,
            **kwargs
        )
        db.session.add(authorization_code)
        db.session.commit()
        return authorization_code

class OAuth2Token(db.Model):
    """
    OAuth2 令牌模型
    """
    __tablename__ = 'oauth2_tokens'
    
    id = Column(Integer, primary_key=True)
    client_id = Column(String(48), ForeignKey('oauth2_clients.client_id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    
    # 令牌信息
    token_type = Column(String(40), default='Bearer')
    access_token = Column(String(255), unique=True, nullable=False, index=True)
    refresh_token = Column(String(255), unique=True, nullable=True, index=True)
    scope = Column(Text, nullable=True)
    
    # 状态
    revoked = Column(Boolean, default=False, nullable=False)
    
    # 时间信息
    issued_at = Column(Integer, nullable=False)
    expires_in = Column(Integer, nullable=False)
    
    # OIDC 支持
    nonce = Column(Text, nullable=True)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # 关系
    user = relationship('User', backref='tokens')
    
    def __repr__(self):
        return f'<OAuth2Token {self.access_token[:10]}...>'
    
    def check_token(self, token):
        """
        验证令牌
        """
        return secrets.compare_digest(self.access_token, token)
    
    def is_refresh_token_active(self):
        """
        检查刷新令牌是否有效
        """
        if self.revoked or not self.refresh_token:
            return False
        return True
    
    def is_access_token_active(self):
        """
        检查访问令牌是否有效
        """
        if self.revoked:
            return False
        
        # 检查是否过期
        current_time = int(time.time())
        expiration_time = self.issued_at + self.expires_in
        return current_time < expiration_time
    
    def revoke(self):
        """
        撤销令牌
        """
        self.revoked = True
        db.session.commit()
    
    def to_dict(self, include_tokens=False):
        """
        转换为字典格式
        """
        data = {
            'id': self.id,
            'client_id': self.client_id,
            'user_id': self.user_id,
            'token_type': self.token_type,
            'scope': self.scope,
            'revoked': self.revoked,
            'issued_at': self.issued_at,
            'expires_in': self.expires_in,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
        
        if include_tokens:
            data.update({
                'access_token': self.access_token,
                'refresh_token': self.refresh_token
            })
        
        return data
    
    @classmethod
    def create_token(cls, client_id, user_id, scope=None, expires_in=3600, **kwargs):
        """
        创建新的访问令牌
        """
        access_token = secrets.token_urlsafe(48)
        refresh_token = secrets.token_urlsafe(48)
        issued_at = int(time.time())
        
        token = cls(
            client_id=client_id,
            user_id=user_id,
            access_token=access_token,
            refresh_token=refresh_token,
            scope=scope,
            issued_at=issued_at,
            expires_in=expires_in,
            **kwargs
        )
        db.session.add(token)
        db.session.commit()
        return token
    
    @classmethod
    def find_by_access_token(cls, access_token):
        """
        根据访问令牌查找令牌记录
        """
        return cls.query.filter_by(access_token=access_token, revoked=False).first()
    
    @classmethod
    def find_by_refresh_token(cls, refresh_token):
        """
        根据刷新令牌查找令牌记录
        """
        return cls.query.filter_by(refresh_token=refresh_token, revoked=False).first()
