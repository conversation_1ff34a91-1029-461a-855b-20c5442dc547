"""
用户数据模型
"""
from api import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.mutable import MutableDict

# 为兼容不同数据库，如果没有 PostgreSQL，则使用 JSON 类型
try:
    JSONType = JSONB
except:
    from sqlalchemy import JSON
    JSONType = JSON

class User(db.Model):
    """
    用户模型
    """
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=True, index=True)
    
    # SAML 相关属性
    saml_subject_id = Column(String(255), unique=True, nullable=True, index=True)
    saml_attributes = Column(MutableDict.as_mutable(JSONType), nullable=True)
    
    # 用户状态
    is_active = Column(db.<PERSON><PERSON>, default=True, nullable=False)
    is_admin = Column(db.<PERSON><PERSON><PERSON>, default=False, nullable=False)
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def to_dict(self, include_sensitive=False):
        """
        转换为字典格式
        """
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }
        
        if include_sensitive:
            data.update({
                'saml_subject_id': self.saml_subject_id,
                'saml_attributes': self.saml_attributes,
                'is_admin': self.is_admin
            })
        
        return data
    
    def update_last_login(self):
        """
        更新最后登录时间
        """
        self.last_login = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def update_saml_attributes(self, attributes):
        """
        更新 SAML 属性
        """
        self.saml_attributes = attributes
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    @classmethod
    def find_by_username(cls, username):
        """
        根据用户名查找用户
        """
        return cls.query.filter_by(username=username).first()
    
    @classmethod
    def find_by_email(cls, email):
        """
        根据邮箱查找用户
        """
        return cls.query.filter_by(email=email).first()
    
    @classmethod
    def find_by_saml_subject_id(cls, subject_id):
        """
        根据 SAML Subject ID 查找用户
        """
        return cls.query.filter_by(saml_subject_id=subject_id).first()
    
    @classmethod
    def create_user(cls, username, email=None, saml_subject_id=None, saml_attributes=None):
        """
        创建新用户
        """
        user = cls(
            username=username,
            email=email,
            saml_subject_id=saml_subject_id,
            saml_attributes=saml_attributes
        )
        db.session.add(user)
        db.session.commit()
        return user
    
    @classmethod
    def get_active_users(cls):
        """
        获取所有活跃用户
        """
        return cls.query.filter_by(is_active=True).all()
    
    @classmethod
    def get_admin_users(cls):
        """
        获取所有管理员用户
        """
        return cls.query.filter_by(is_admin=True, is_active=True).all()
