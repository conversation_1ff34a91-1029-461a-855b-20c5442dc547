"""
路由模块
"""
from flask import Blueprint

def register_blueprints(app):
    """
    注册所有蓝图
    """
    # 导入蓝图
    from .health import health_bp
    from .users import users_bp
    from .oauth import oauth_bp
    from .auth import auth_bp
    
    # 注册蓝图
    app.register_blueprint(health_bp, url_prefix='/api/v1')
    app.register_blueprint(users_bp, url_prefix='/api/v1/users')
    app.register_blueprint(oauth_bp, url_prefix='/api/v1/oauth')
    app.register_blueprint(auth_bp, url_prefix='/api/v1/auth')
