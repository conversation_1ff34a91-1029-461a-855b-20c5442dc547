"""
认证相关路由
"""
from flask import Blueprint, request, session, jsonify
from api import db
from api.models import User, OAuth2Token
from api.utils.auth import get_current_user, optional_auth
from api.utils.response import (
    success_response, error_response, unauthorized_response
)
from api.utils.validation import validate_json
from marshmallow import Schema, fields, validate
from datetime import datetime

auth_bp = Blueprint('auth', __name__)

# 登录模式（如果需要本地认证）
class LoginSchema(Schema):
    username = fields.String(required=True, validate=validate.Length(min=3, max=80))
    password = fields.String(required=True, validate=validate.Length(min=6))

# 令牌验证模式
class TokenValidationSchema(Schema):
    token = fields.String(required=True)

login_schema = LoginSchema()
token_validation_schema = TokenValidationSchema()

@auth_bp.route('/status', methods=['GET'])
@optional_auth
def auth_status():
    """
    获取当前认证状态
    """
    try:
        user = getattr(request, 'current_user', None)
        
        if user:
            # 检查是否通过 OAuth 令牌认证
            oauth_token = getattr(request, 'oauth_token', None)
            auth_method = 'oauth' if oauth_token else 'session'
            
            return success_response(
                message="用户已认证",
                data={
                    'authenticated': True,
                    'user': user.to_dict(include_sensitive=False),
                    'auth_method': auth_method,
                    'token_info': oauth_token.to_dict(include_tokens=False) if oauth_token else None
                }
            )
        else:
            return success_response(
                message="用户未认证",
                data={
                    'authenticated': False,
                    'user': None,
                    'auth_method': None,
                    'token_info': None
                }
            )
            
    except Exception as e:
        return error_response('system_error', f'获取认证状态失败: {str(e)}', 500)

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """
    用户登出
    """
    try:
        # 清除会话
        session.clear()
        
        # 如果是通过 OAuth 令牌认证，可以选择撤销令牌
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            oauth_token = OAuth2Token.find_by_access_token(token)
            if oauth_token:
                # 可以选择是否撤销令牌，这里只是标记但不撤销
                # oauth_token.revoke()
                pass
        
        return success_response(message="登出成功")
        
    except Exception as e:
        return error_response('system_error', f'登出失败: {str(e)}', 500)

@auth_bp.route('/validate-token', methods=['POST'])
@validate_json(token_validation_schema)
def validate_token():
    """
    验证访问令牌
    """
    try:
        data = request.validated_data
        token = data['token']
        
        # 查找令牌
        oauth_token = OAuth2Token.find_by_access_token(token)
        if not oauth_token:
            return error_response('invalid_token', '令牌不存在', 401)
        
        # 检查令牌是否有效
        if not oauth_token.is_access_token_active():
            return error_response('token_expired', '令牌已过期或被撤销', 401)
        
        # 获取用户信息
        user = User.query.get(oauth_token.user_id)
        if not user or not user.is_active:
            return error_response('invalid_user', '用户不存在或已被停用', 401)
        
        return success_response(
            message="令牌验证成功",
            data={
                'valid': True,
                'token_info': oauth_token.to_dict(include_tokens=False),
                'user': user.to_dict(include_sensitive=False)
            }
        )
        
    except Exception as e:
        return error_response('system_error', f'令牌验证失败: {str(e)}', 500)

@auth_bp.route('/token-info', methods=['GET'])
def token_info():
    """
    获取当前令牌信息
    """
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return unauthorized_response("缺少访问令牌")
        
        token = auth_header.split(' ')[1]
        oauth_token = OAuth2Token.find_by_access_token(token)
        
        if not oauth_token:
            return error_response('invalid_token', '令牌不存在', 401)
        
        if not oauth_token.is_access_token_active():
            return error_response('token_expired', '令牌已过期或被撤销', 401)
        
        # 获取用户信息
        user = User.query.get(oauth_token.user_id)
        if not user or not user.is_active:
            return error_response('invalid_user', '用户不存在或已被停用', 401)
        
        # 计算令牌剩余时间
        from api.utils.security import get_token_remaining_time
        remaining_time = get_token_remaining_time(oauth_token.issued_at, oauth_token.expires_in)
        
        return success_response(
            message="令牌信息获取成功",
            data={
                'token_info': oauth_token.to_dict(include_tokens=False),
                'user': user.to_dict(include_sensitive=False),
                'remaining_time': remaining_time,
                'expires_at': oauth_token.issued_at + oauth_token.expires_in
            }
        )
        
    except Exception as e:
        return error_response('system_error', f'获取令牌信息失败: {str(e)}', 500)

@auth_bp.route('/revoke-token', methods=['POST'])
def revoke_token():
    """
    撤销当前令牌
    """
    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return unauthorized_response("缺少访问令牌")
        
        token = auth_header.split(' ')[1]
        oauth_token = OAuth2Token.find_by_access_token(token)
        
        if not oauth_token:
            return error_response('invalid_token', '令牌不存在', 401)
        
        # 撤销令牌
        oauth_token.revoke()
        
        return success_response(message="令牌撤销成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'撤销令牌失败: {str(e)}', 500)

@auth_bp.route('/user-tokens', methods=['GET'])
def list_user_tokens():
    """
    获取当前用户的令牌列表
    """
    try:
        user = get_current_user()
        if not user:
            return unauthorized_response()
        
        # 获取用户的所有令牌
        tokens = OAuth2Token.query.filter_by(user_id=user.id).order_by(OAuth2Token.created_at.desc()).all()
        
        tokens_data = []
        for token in tokens:
            token_data = token.to_dict(include_tokens=False)
            # 添加令牌状态信息
            token_data['is_active'] = token.is_access_token_active()
            if token_data['is_active']:
                from api.utils.security import get_token_remaining_time
                token_data['remaining_time'] = get_token_remaining_time(token.issued_at, token.expires_in)
            tokens_data.append(token_data)
        
        return success_response(
            message="用户令牌列表获取成功",
            data={
                'tokens': tokens_data,
                'total': len(tokens_data)
            }
        )
        
    except Exception as e:
        return error_response('system_error', f'获取用户令牌列表失败: {str(e)}', 500)

@auth_bp.route('/revoke-all-tokens', methods=['POST'])
def revoke_all_user_tokens():
    """
    撤销当前用户的所有令牌
    """
    try:
        user = get_current_user()
        if not user:
            return unauthorized_response()
        
        # 撤销用户的所有活跃令牌
        active_tokens = OAuth2Token.query.filter_by(user_id=user.id, revoked=False).all()
        
        revoked_count = 0
        for token in active_tokens:
            token.revoke()
            revoked_count += 1
        
        return success_response(
            message=f"成功撤销 {revoked_count} 个令牌",
            data={'revoked_count': revoked_count}
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'撤销令牌失败: {str(e)}', 500)

@auth_bp.route('/permissions', methods=['GET'])
def get_user_permissions():
    """
    获取当前用户权限信息
    """
    try:
        user = get_current_user()
        if not user:
            return unauthorized_response()
        
        # 基本权限
        permissions = {
            'can_read_own_profile': True,
            'can_update_own_profile': True,
            'can_view_own_tokens': True,
            'can_revoke_own_tokens': True
        }
        
        # 管理员权限
        if user.is_admin:
            permissions.update({
                'can_manage_users': True,
                'can_manage_oauth_clients': True,
                'can_manage_oauth_tokens': True,
                'can_view_system_stats': True,
                'can_view_system_config': True
            })
        
        # OAuth 令牌权限
        oauth_token = getattr(request, 'oauth_token', None)
        if oauth_token:
            scopes = oauth_token.scope.split() if oauth_token.scope else []
            permissions['oauth_scopes'] = scopes
            permissions['auth_method'] = 'oauth'
        else:
            permissions['auth_method'] = 'session'
        
        return success_response(
            message="用户权限信息获取成功",
            data={
                'user': user.to_dict(include_sensitive=False),
                'permissions': permissions
            }
        )
        
    except Exception as e:
        return error_response('system_error', f'获取权限信息失败: {str(e)}', 500)
