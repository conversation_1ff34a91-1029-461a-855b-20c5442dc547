"""
健康检查和系统信息路由
"""
from flask import Blueprint, jsonify, request
from api import db
from api.models import User, OAuth2Client, OAuth2Token, OAuth2AuthorizationCode
from api.utils.response import success_response, error_response
from datetime import datetime
import sys
import os

health_bp = Blueprint('health', __name__)

@health_bp.route('/health', methods=['GET'])
def health_check():
    """
    健康检查端点
    """
    try:
        # 检查数据库连接
        db.session.execute('SELECT 1')
        db_status = 'healthy'
    except Exception as e:
        db_status = f'error: {str(e)}'
    
    # 检查 Redis 连接（如果配置了）
    redis_status = 'not_configured'
    try:
        import redis
        from config import load_config
        config = load_config()
        redis_config = config.get('redis', {})
        if redis_config:
            r = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0)
            )
            r.ping()
            redis_status = 'healthy'
    except Exception as e:
        redis_status = f'error: {str(e)}'
    
    health_data = {
        'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'services': {
            'database': db_status,
            'redis': redis_status
        },
        'system': {
            'python_version': sys.version,
            'platform': sys.platform
        }
    }
    
    status_code = 200 if health_data['status'] == 'healthy' else 503
    return jsonify(health_data), status_code

@health_bp.route('/info', methods=['GET'])
def system_info():
    """
    系统信息端点
    """
    try:
        from config import load_config
        config = load_config()
        
        info_data = {
            'name': 'SAML2 SP + OIDC IdP API System',
            'description': '基于 Flask 的 SAML2 服务提供者和 OIDC 身份提供者 API 系统',
            'version': '1.0.0',
            'api_version': 'v1',
            'endpoints': {
                'health': '/api/v1/health',
                'info': '/api/v1/info',
                'stats': '/api/v1/stats',
                'config': '/api/v1/config',
                'users': '/api/v1/users',
                'oauth_clients': '/api/v1/oauth/clients',
                'oauth_tokens': '/api/v1/oauth/tokens',
                'auth': '/api/v1/auth'
            },
            'features': [
                'User Management API',
                'OAuth2 Client Management',
                'OAuth2 Token Management',
                'Authentication API',
                'Health Monitoring',
                'PostgreSQL Support',
                'RESTful Design'
            ],
            'supported_operations': [
                'CRUD Operations for Users',
                'CRUD Operations for OAuth2 Clients',
                'Token Management',
                'Authentication & Authorization',
                'Pagination Support',
                'Data Validation'
            ]
        }
        
        return jsonify(info_data)
    except Exception as e:
        return error_response('system_error', f'获取系统信息失败: {str(e)}', 500)

@health_bp.route('/stats', methods=['GET'])
def system_stats():
    """
    系统统计信息端点
    """
    try:
        # 统计用户数量
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        admin_users = User.query.filter_by(is_admin=True, is_active=True).count()
        
        # 统计 OAuth2 客户端数量
        total_clients = OAuth2Client.query.count()
        active_clients = OAuth2Client.query.filter_by(is_active=True).count()
        
        # 统计令牌数量
        total_tokens = OAuth2Token.query.count()
        active_tokens = OAuth2Token.query.filter_by(revoked=False).count()
        
        # 统计授权码数量
        total_codes = OAuth2AuthorizationCode.query.count()
        unused_codes = OAuth2AuthorizationCode.query.filter_by(is_used=False).count()
        
        stats_data = {
            'users': {
                'total': total_users,
                'active': active_users,
                'admin': admin_users
            },
            'oauth2': {
                'clients': {
                    'total': total_clients,
                    'active': active_clients
                },
                'tokens': {
                    'total': total_tokens,
                    'active': active_tokens
                },
                'authorization_codes': {
                    'total': total_codes,
                    'unused': unused_codes
                }
            },
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return jsonify(stats_data)
    except Exception as e:
        return error_response('system_error', f'获取统计信息失败: {str(e)}', 500)

@health_bp.route('/config', methods=['GET'])
def system_config():
    """
    系统配置信息端点（仅返回公开配置）
    """
    try:
        from config import load_config
        config = load_config()
        
        # 只返回公开的配置信息，不包含敏感数据
        public_config = {
            'saml': {
                'entity_id': config.get('saml', {}).get('entity_id'),
                'acs_url': config.get('saml', {}).get('acs_url'),
                'idp_entity_id': config.get('saml', {}).get('idp_entity_id')
            },
            'oidc': {
                'issuer': config.get('oidc', {}).get('issuer'),
                'access_token_exp': config.get('oidc', {}).get('access_token_exp'),
                'id_token_exp': config.get('oidc', {}).get('id_token_exp')
            },
            'database': {
                'type': 'postgresql' if 'postgresql' in config.get('database', {}).get('url', '') else 'sqlite'
            },
            'api': {
                'version': 'v1',
                'pagination': {
                    'default_per_page': 20,
                    'max_per_page': 100
                }
            }
        }
        
        return jsonify(public_config)
    except Exception as e:
        return error_response('system_error', f'获取配置信息失败: {str(e)}', 500)
