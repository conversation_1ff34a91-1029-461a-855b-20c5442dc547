"""
OAuth2 管理路由
"""
from flask import Blueprint, request, jsonify
from api import db
from api.models import OAuth2Client, OAuth2Token, OAuth2AuthorizationCode, User
from api.utils.auth import require_auth, require_admin
from api.utils.response import (
    success_response, error_response, not_found_response,
    paginated_response, conflict_response
)
from api.utils.pagination import paginate_with_filters_and_sorting
from api.utils.validation import validate_json
from api.utils.security import generate_client_credentials
from api.schemas import OAuth2ClientSchema, OAuth2TokenSchema
from marshmallow import Schema, fields, validate
from datetime import datetime

oauth_bp = Blueprint('oauth', __name__)

# OAuth2 客户端创建模式
class OAuth2ClientCreateSchema(Schema):
    client_name = fields.String(required=True, validate=validate.Length(min=1, max=120))
    client_uri = fields.Url(allow_none=True)
    logo_uri = fields.Url(allow_none=True)
    contact = fields.String(allow_none=True)
    tos_uri = fields.Url(allow_none=True)
    policy_uri = fields.Url(allow_none=True)
    redirect_uris = fields.List(fields.Url(), required=True, validate=validate.Length(min=1))
    grant_types = fields.List(fields.String(), missing=['authorization_code'])
    response_types = fields.List(fields.String(), missing=['code'])
    token_endpoint_auth_method = fields.String(missing='client_secret_basic')
    scope = fields.String(missing='openid')

# OAuth2 客户端更新模式
class OAuth2ClientUpdateSchema(Schema):
    client_name = fields.String(validate=validate.Length(min=1, max=120))
    client_uri = fields.Url(allow_none=True)
    logo_uri = fields.Url(allow_none=True)
    contact = fields.String(allow_none=True)
    tos_uri = fields.Url(allow_none=True)
    policy_uri = fields.Url(allow_none=True)
    redirect_uris = fields.List(fields.Url(), validate=validate.Length(min=1))
    grant_types = fields.List(fields.String())
    response_types = fields.List(fields.String())
    token_endpoint_auth_method = fields.String()
    scope = fields.String()
    is_active = fields.Boolean()

oauth_client_create_schema = OAuth2ClientCreateSchema()
oauth_client_update_schema = OAuth2ClientUpdateSchema()
oauth_client_schema = OAuth2ClientSchema()
oauth_clients_schema = OAuth2ClientSchema(many=True)

# ==================== OAuth2 客户端管理 ====================

@oauth_bp.route('/clients', methods=['GET'])
@require_admin
def list_oauth_clients():
    """
    获取 OAuth2 客户端列表（仅管理员）
    """
    try:
        # 允许排序的字段
        allowed_sort_fields = ['id', 'client_name', 'created_at', 'updated_at']
        
        # 构建查询
        query = OAuth2Client.query
        
        # 应用分页、过滤和排序
        result = paginate_with_filters_and_sorting(query, OAuth2Client, allowed_sort_fields)
        
        # 序列化客户端数据（不包含密钥）
        clients_data = [client.to_dict(include_secret=False) for client in result['items']]
        
        return paginated_response(
            items=clients_data,
            pagination_info=result['pagination'],
            message="OAuth2 客户端列表获取成功"
        )
        
    except Exception as e:
        return error_response('system_error', f'获取客户端列表失败: {str(e)}', 500)

@oauth_bp.route('/clients/<int:client_id>', methods=['GET'])
@require_admin
def get_oauth_client(client_id):
    """
    获取单个 OAuth2 客户端信息（仅管理员）
    """
    try:
        client = OAuth2Client.query.get(client_id)
        if not client:
            return not_found_response("OAuth2 客户端")
        
        return success_response(
            message="OAuth2 客户端信息获取成功",
            data=client.to_dict(include_secret=False)
        )
        
    except Exception as e:
        return error_response('system_error', f'获取客户端信息失败: {str(e)}', 500)

@oauth_bp.route('/clients', methods=['POST'])
@require_admin
@validate_json(oauth_client_create_schema)
def create_oauth_client():
    """
    创建新的 OAuth2 客户端（仅管理员）
    """
    try:
        data = request.validated_data
        
        # 检查客户端名称是否已存在
        existing_client = OAuth2Client.query.filter_by(client_name=data['client_name']).first()
        if existing_client:
            return conflict_response("客户端名称已存在")
        
        # 创建客户端
        client = OAuth2Client.create_client(
            client_name=data['client_name'],
            redirect_uris=data['redirect_uris'],
            client_uri=data.get('client_uri'),
            logo_uri=data.get('logo_uri'),
            contact=data.get('contact'),
            tos_uri=data.get('tos_uri'),
            policy_uri=data.get('policy_uri'),
            grant_types=data.get('grant_types', ['authorization_code']),
            response_types=data.get('response_types', ['code']),
            token_endpoint_auth_method=data.get('token_endpoint_auth_method', 'client_secret_basic'),
            scope=data.get('scope', 'openid')
        )
        
        # 返回包含密钥的完整信息（仅在创建时返回）
        return success_response(
            message="OAuth2 客户端创建成功",
            data=client.to_dict(include_secret=True),
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'创建客户端失败: {str(e)}', 500)

@oauth_bp.route('/clients/<int:client_id>', methods=['PUT'])
@require_admin
@validate_json(oauth_client_update_schema)
def update_oauth_client(client_id):
    """
    更新 OAuth2 客户端信息（仅管理员）
    """
    try:
        client = OAuth2Client.query.get(client_id)
        if not client:
            return not_found_response("OAuth2 客户端")
        
        data = request.validated_data
        
        # 检查客户端名称是否已被其他客户端使用
        if 'client_name' in data and data['client_name'] != client.client_name:
            existing_client = OAuth2Client.query.filter_by(client_name=data['client_name']).first()
            if existing_client and existing_client.id != client.id:
                return conflict_response("客户端名称已存在")
        
        # 更新客户端信息
        for key, value in data.items():
            if hasattr(client, key):
                setattr(client, key, value)
        
        client.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            message="OAuth2 客户端信息更新成功",
            data=client.to_dict(include_secret=False)
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'更新客户端信息失败: {str(e)}', 500)

@oauth_bp.route('/clients/<int:client_id>', methods=['DELETE'])
@require_admin
def delete_oauth_client(client_id):
    """
    删除 OAuth2 客户端（仅管理员）
    """
    try:
        client = OAuth2Client.query.get(client_id)
        if not client:
            return not_found_response("OAuth2 客户端")
        
        # 软删除：设置为非活跃状态
        client.is_active = False
        client.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(message="OAuth2 客户端删除成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'删除客户端失败: {str(e)}', 500)

@oauth_bp.route('/clients/<int:client_id>/regenerate-secret', methods=['POST'])
@require_admin
def regenerate_client_secret(client_id):
    """
    重新生成客户端密钥（仅管理员）
    """
    try:
        client = OAuth2Client.query.get(client_id)
        if not client:
            return not_found_response("OAuth2 客户端")
        
        # 生成新的客户端密钥
        _, new_secret = generate_client_credentials()
        client.client_secret = new_secret
        client.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            message="客户端密钥重新生成成功",
            data={
                'client_id': client.client_id,
                'client_secret': new_secret
            }
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'重新生成密钥失败: {str(e)}', 500)

# ==================== OAuth2 令牌管理 ====================

@oauth_bp.route('/tokens', methods=['GET'])
@require_admin
def list_oauth_tokens():
    """
    获取 OAuth2 令牌列表（仅管理员）
    """
    try:
        # 允许排序的字段
        allowed_sort_fields = ['id', 'client_id', 'user_id', 'issued_at', 'created_at']
        
        # 构建查询
        query = OAuth2Token.query
        
        # 应用分页、过滤和排序
        result = paginate_with_filters_and_sorting(query, OAuth2Token, allowed_sort_fields)
        
        # 序列化令牌数据（不包含实际令牌）
        tokens_data = [token.to_dict(include_tokens=False) for token in result['items']]
        
        return paginated_response(
            items=tokens_data,
            pagination_info=result['pagination'],
            message="OAuth2 令牌列表获取成功"
        )
        
    except Exception as e:
        return error_response('system_error', f'获取令牌列表失败: {str(e)}', 500)

@oauth_bp.route('/tokens/<int:token_id>', methods=['GET'])
@require_admin
def get_oauth_token(token_id):
    """
    获取单个 OAuth2 令牌信息（仅管理员）
    """
    try:
        token = OAuth2Token.query.get(token_id)
        if not token:
            return not_found_response("OAuth2 令牌")
        
        return success_response(
            message="OAuth2 令牌信息获取成功",
            data=token.to_dict(include_tokens=False)
        )
        
    except Exception as e:
        return error_response('system_error', f'获取令牌信息失败: {str(e)}', 500)

@oauth_bp.route('/tokens/<int:token_id>/revoke', methods=['POST'])
@require_admin
def revoke_oauth_token(token_id):
    """
    撤销 OAuth2 令牌（仅管理员）
    """
    try:
        token = OAuth2Token.query.get(token_id)
        if not token:
            return not_found_response("OAuth2 令牌")
        
        token.revoke()
        
        return success_response(
            message="OAuth2 令牌撤销成功",
            data=token.to_dict(include_tokens=False)
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'撤销令牌失败: {str(e)}', 500)

@oauth_bp.route('/tokens/user/<int:user_id>', methods=['GET'])
@require_admin
def list_user_tokens(user_id):
    """
    获取指定用户的令牌列表（仅管理员）
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return not_found_response("用户")
        
        # 允许排序的字段
        allowed_sort_fields = ['id', 'client_id', 'issued_at', 'created_at']
        
        # 构建查询
        query = OAuth2Token.query.filter_by(user_id=user_id)
        
        # 应用分页、过滤和排序
        result = paginate_with_filters_and_sorting(query, OAuth2Token, allowed_sort_fields)
        
        # 序列化令牌数据
        tokens_data = [token.to_dict(include_tokens=False) for token in result['items']]
        
        return paginated_response(
            items=tokens_data,
            pagination_info=result['pagination'],
            message=f"用户 {user.username} 的令牌列表获取成功"
        )
        
    except Exception as e:
        return error_response('system_error', f'获取用户令牌列表失败: {str(e)}', 500)

@oauth_bp.route('/tokens/client/<client_id>', methods=['GET'])
@require_admin
def list_client_tokens(client_id):
    """
    获取指定客户端的令牌列表（仅管理员）
    """
    try:
        client = OAuth2Client.find_by_client_id(client_id)
        if not client:
            return not_found_response("OAuth2 客户端")
        
        # 允许排序的字段
        allowed_sort_fields = ['id', 'user_id', 'issued_at', 'created_at']
        
        # 构建查询
        query = OAuth2Token.query.filter_by(client_id=client_id)
        
        # 应用分页、过滤和排序
        result = paginate_with_filters_and_sorting(query, OAuth2Token, allowed_sort_fields)
        
        # 序列化令牌数据
        tokens_data = [token.to_dict(include_tokens=False) for token in result['items']]
        
        return paginated_response(
            items=tokens_data,
            pagination_info=result['pagination'],
            message=f"客户端 {client.client_name} 的令牌列表获取成功"
        )

    except Exception as e:
        return error_response('system_error', f'获取客户端令牌列表失败: {str(e)}', 500)

# ==================== OAuth2 授权码管理 ====================

@oauth_bp.route('/codes', methods=['GET'])
@require_admin
def list_authorization_codes():
    """
    获取授权码列表（仅管理员）
    """
    try:
        # 允许排序的字段
        allowed_sort_fields = ['id', 'client_id', 'user_id', 'auth_time', 'created_at']

        # 构建查询
        query = OAuth2AuthorizationCode.query

        # 应用分页、过滤和排序
        result = paginate_with_filters_and_sorting(query, OAuth2AuthorizationCode, allowed_sort_fields)

        # 序列化授权码数据
        codes_data = [code.to_dict() for code in result['items']]

        return paginated_response(
            items=codes_data,
            pagination_info=result['pagination'],
            message="授权码列表获取成功"
        )

    except Exception as e:
        return error_response('system_error', f'获取授权码列表失败: {str(e)}', 500)

@oauth_bp.route('/codes/<int:code_id>', methods=['GET'])
@require_admin
def get_authorization_code(code_id):
    """
    获取单个授权码信息（仅管理员）
    """
    try:
        code = OAuth2AuthorizationCode.query.get(code_id)
        if not code:
            return not_found_response("授权码")

        return success_response(
            message="授权码信息获取成功",
            data=code.to_dict()
        )

    except Exception as e:
        return error_response('system_error', f'获取授权码信息失败: {str(e)}', 500)

# ==================== 统计信息 ====================

@oauth_bp.route('/stats', methods=['GET'])
@require_admin
def oauth_stats():
    """
    获取 OAuth2 统计信息（仅管理员）
    """
    try:
        # 客户端统计
        total_clients = OAuth2Client.query.count()
        active_clients = OAuth2Client.query.filter_by(is_active=True).count()

        # 令牌统计
        total_tokens = OAuth2Token.query.count()
        active_tokens = OAuth2Token.query.filter_by(revoked=False).count()
        revoked_tokens = OAuth2Token.query.filter_by(revoked=True).count()

        # 授权码统计
        total_codes = OAuth2AuthorizationCode.query.count()
        used_codes = OAuth2AuthorizationCode.query.filter_by(is_used=True).count()
        unused_codes = OAuth2AuthorizationCode.query.filter_by(is_used=False).count()

        # 按客户端统计令牌数量
        from sqlalchemy import func
        client_token_stats = db.session.query(
            OAuth2Client.client_name,
            func.count(OAuth2Token.id).label('token_count')
        ).outerjoin(OAuth2Token).group_by(OAuth2Client.id, OAuth2Client.client_name).all()

        stats_data = {
            'clients': {
                'total': total_clients,
                'active': active_clients,
                'inactive': total_clients - active_clients
            },
            'tokens': {
                'total': total_tokens,
                'active': active_tokens,
                'revoked': revoked_tokens
            },
            'authorization_codes': {
                'total': total_codes,
                'used': used_codes,
                'unused': unused_codes
            },
            'client_token_distribution': [
                {
                    'client_name': stat[0],
                    'token_count': stat[1]
                }
                for stat in client_token_stats
            ],
            'timestamp': datetime.utcnow().isoformat()
        }

        return success_response(
            message="OAuth2 统计信息获取成功",
            data=stats_data
        )

    except Exception as e:
        return error_response('system_error', f'获取统计信息失败: {str(e)}', 500)
