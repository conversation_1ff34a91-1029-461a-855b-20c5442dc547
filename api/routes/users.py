"""
用户管理路由
"""
from flask import Blueprint, request, jsonify
from api import db
from api.models import User
from api.utils.auth import require_auth, require_admin, require_self_or_admin
from api.utils.response import (
    success_response, error_response, not_found_response,
    paginated_response, conflict_response
)
from api.utils.pagination import paginate_with_filters_and_sorting
from api.utils.validation import validate_json
from api.schemas import UserSchema, PaginationSchema
from marshmallow import Schema, fields, validate
from datetime import datetime

users_bp = Blueprint('users', __name__)

# 用户创建模式
class UserCreateSchema(Schema):
    username = fields.String(required=True, validate=validate.Length(min=3, max=80))
    email = fields.Email(allow_none=True)
    is_admin = fields.Boolean(missing=False)

# 用户更新模式
class UserUpdateSchema(Schema):
    username = fields.String(validate=validate.Length(min=3, max=80))
    email = fields.Email(allow_none=True)
    is_active = fields.Boolean()
    is_admin = fields.Boolean()

user_create_schema = UserCreateSchema()
user_update_schema = UserUpdateSchema()
user_schema = UserSchema()
users_schema = UserSchema(many=True)

@users_bp.route('', methods=['GET'])
@require_auth
def list_users():
    """
    获取用户列表
    """
    try:
        # 允许排序的字段
        allowed_sort_fields = ['id', 'username', 'email', 'created_at', 'last_login']
        
        # 构建查询
        query = User.query
        
        # 应用分页、过滤和排序
        result = paginate_with_filters_and_sorting(query, User, allowed_sort_fields)
        
        # 序列化用户数据
        users_data = []
        for user in result['items']:
            # 只有管理员可以看到敏感信息
            include_sensitive = hasattr(request, 'current_user') and request.current_user.is_admin
            users_data.append(user.to_dict(include_sensitive=include_sensitive))
        
        return paginated_response(
            items=users_data,
            pagination_info=result['pagination'],
            message="用户列表获取成功"
        )
        
    except Exception as e:
        return error_response('system_error', f'获取用户列表失败: {str(e)}', 500)

@users_bp.route('/<int:user_id>', methods=['GET'])
@require_self_or_admin('user_id')
def get_user(user_id):
    """
    获取单个用户信息
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return not_found_response("用户")
        
        # 检查权限：用户只能查看自己的信息，管理员可以查看所有用户信息
        current_user = request.current_user
        include_sensitive = (current_user.id == user.id) or current_user.is_admin
        
        return success_response(
            message="用户信息获取成功",
            data=user.to_dict(include_sensitive=include_sensitive)
        )
        
    except Exception as e:
        return error_response('system_error', f'获取用户信息失败: {str(e)}', 500)

@users_bp.route('', methods=['POST'])
@require_admin
@validate_json(user_create_schema)
def create_user():
    """
    创建新用户（仅管理员）
    """
    try:
        data = request.validated_data
        
        # 检查用户名是否已存在
        if User.find_by_username(data['username']):
            return conflict_response("用户名已存在")
        
        # 检查邮箱是否已存在
        if data.get('email') and User.find_by_email(data['email']):
            return conflict_response("邮箱已存在")
        
        # 创建用户
        user = User.create_user(
            username=data['username'],
            email=data.get('email'),
            is_admin=data.get('is_admin', False)
        )
        
        return success_response(
            message="用户创建成功",
            data=user.to_dict(include_sensitive=True),
            status_code=201
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'创建用户失败: {str(e)}', 500)

@users_bp.route('/<int:user_id>', methods=['PUT'])
@require_self_or_admin('user_id')
@validate_json(user_update_schema)
def update_user(user_id):
    """
    更新用户信息
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return not_found_response("用户")
        
        data = request.validated_data
        current_user = request.current_user
        
        # 检查权限：普通用户只能修改自己的基本信息，管理员可以修改所有信息
        if current_user.id != user.id and not current_user.is_admin:
            return error_response('forbidden', '没有权限修改此用户信息', 403)
        
        # 普通用户不能修改管理员状态和激活状态
        if current_user.id == user.id and not current_user.is_admin:
            data.pop('is_admin', None)
            data.pop('is_active', None)
        
        # 检查用户名是否已被其他用户使用
        if 'username' in data and data['username'] != user.username:
            existing_user = User.find_by_username(data['username'])
            if existing_user and existing_user.id != user.id:
                return conflict_response("用户名已存在")
        
        # 检查邮箱是否已被其他用户使用
        if 'email' in data and data['email'] != user.email:
            existing_user = User.find_by_email(data['email'])
            if existing_user and existing_user.id != user.id:
                return conflict_response("邮箱已存在")
        
        # 更新用户信息
        for key, value in data.items():
            if hasattr(user, key):
                setattr(user, key, value)
        
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            message="用户信息更新成功",
            data=user.to_dict(include_sensitive=current_user.is_admin)
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'更新用户信息失败: {str(e)}', 500)

@users_bp.route('/<int:user_id>', methods=['DELETE'])
@require_admin
def delete_user(user_id):
    """
    删除用户（仅管理员）
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return not_found_response("用户")
        
        # 不能删除自己
        if request.current_user.id == user.id:
            return error_response('forbidden', '不能删除自己的账户', 403)
        
        # 软删除：设置为非活跃状态
        user.is_active = False
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(message="用户删除成功")
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'删除用户失败: {str(e)}', 500)

@users_bp.route('/<int:user_id>/activate', methods=['POST'])
@require_admin
def activate_user(user_id):
    """
    激活用户（仅管理员）
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return not_found_response("用户")
        
        user.is_active = True
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            message="用户激活成功",
            data=user.to_dict(include_sensitive=True)
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'激活用户失败: {str(e)}', 500)

@users_bp.route('/<int:user_id>/deactivate', methods=['POST'])
@require_admin
def deactivate_user(user_id):
    """
    停用用户（仅管理员）
    """
    try:
        user = User.query.get(user_id)
        if not user:
            return not_found_response("用户")
        
        # 不能停用自己
        if request.current_user.id == user.id:
            return error_response('forbidden', '不能停用自己的账户', 403)
        
        user.is_active = False
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            message="用户停用成功",
            data=user.to_dict(include_sensitive=True)
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'停用用户失败: {str(e)}', 500)

@users_bp.route('/me', methods=['GET'])
@require_auth
def get_current_user_info():
    """
    获取当前用户信息
    """
    try:
        user = request.current_user
        return success_response(
            message="当前用户信息获取成功",
            data=user.to_dict(include_sensitive=True)
        )
        
    except Exception as e:
        return error_response('system_error', f'获取用户信息失败: {str(e)}', 500)

@users_bp.route('/me', methods=['PUT'])
@require_auth
@validate_json(user_update_schema)
def update_current_user():
    """
    更新当前用户信息
    """
    try:
        user = request.current_user
        data = request.validated_data
        
        # 普通用户不能修改管理员状态和激活状态
        if not user.is_admin:
            data.pop('is_admin', None)
            data.pop('is_active', None)
        
        # 检查用户名是否已被其他用户使用
        if 'username' in data and data['username'] != user.username:
            existing_user = User.find_by_username(data['username'])
            if existing_user and existing_user.id != user.id:
                return conflict_response("用户名已存在")
        
        # 检查邮箱是否已被其他用户使用
        if 'email' in data and data['email'] != user.email:
            existing_user = User.find_by_email(data['email'])
            if existing_user and existing_user.id != user.id:
                return conflict_response("邮箱已存在")
        
        # 更新用户信息
        for key, value in data.items():
            if hasattr(user, key):
                setattr(user, key, value)
        
        user.updated_at = datetime.utcnow()
        db.session.commit()
        
        return success_response(
            message="用户信息更新成功",
            data=user.to_dict(include_sensitive=True)
        )
        
    except Exception as e:
        db.session.rollback()
        return error_response('system_error', f'更新用户信息失败: {str(e)}', 500)
