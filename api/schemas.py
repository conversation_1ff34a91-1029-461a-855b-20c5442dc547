"""
API 数据序列化模式定义
使用 Marshmallow 进行数据验证和序列化
"""
from marshmallow import Schema, fields, validate, ValidationError
from datetime import datetime

class UserSchema(Schema):
    """用户数据模式"""
    id = fields.Integer(dump_only=True)
    username = fields.String(required=True, validate=validate.Length(min=3, max=80))
    email = fields.Email(allow_none=True)
    saml_subject_id = fields.String(dump_only=True)
    saml_attributes = fields.Dict(dump_only=True)
    created_at = fields.DateTime(dump_only=True)
    last_login = fields.DateTime(dump_only=True)

class OAuth2ClientSchema(Schema):
    """OAuth2 客户端数据模式"""
    id = fields.Integer(dump_only=True)
    client_id = fields.String(required=True, validate=validate.Length(min=10, max=48))
    client_secret = fields.String(required=True, load_only=True)
    client_name = fields.String(required=True, validate=validate.Length(min=1, max=120))
    client_uri = fields.Url(allow_none=True)
    logo_uri = fields.Url(allow_none=True)
    contact = fields.String(allow_none=True)
    tos_uri = fields.Url(allow_none=True)
    policy_uri = fields.Url(allow_none=True)
    redirect_uris = fields.List(fields.Url(), required=True, validate=validate.Length(min=1))
    grant_types = fields.List(fields.String(), missing=['authorization_code'])
    response_types = fields.List(fields.String(), missing=['code'])
    token_endpoint_auth_method = fields.String(missing='client_secret_basic')
    scope = fields.String(missing='openid')
    created_at = fields.DateTime(dump_only=True)

class OAuth2TokenSchema(Schema):
    """OAuth2 令牌数据模式"""
    id = fields.Integer(dump_only=True)
    client_id = fields.String(dump_only=True)
    user_id = fields.Integer(dump_only=True)
    token_type = fields.String(dump_only=True)
    access_token = fields.String(dump_only=True)
    refresh_token = fields.String(dump_only=True)
    scope = fields.String(dump_only=True)
    revoked = fields.Integer(dump_only=True)
    issued_at = fields.Integer(dump_only=True)
    expires_in = fields.Integer(dump_only=True)

class OAuth2AuthorizationCodeSchema(Schema):
    """OAuth2 授权码数据模式"""
    id = fields.Integer(dump_only=True)
    code = fields.String(dump_only=True)
    client_id = fields.String(dump_only=True)
    redirect_uri = fields.Url(dump_only=True)
    response_type = fields.String(dump_only=True)
    scope = fields.String(dump_only=True)
    auth_time = fields.Integer(dump_only=True)
    user_id = fields.Integer(dump_only=True)
    created_at = fields.DateTime(dump_only=True)

class ErrorSchema(Schema):
    """错误响应模式"""
    error = fields.String(required=True)
    message = fields.String(required=True)
    code = fields.Integer(missing=400)

class SuccessSchema(Schema):
    """成功响应模式"""
    success = fields.Boolean(missing=True)
    message = fields.String(required=True)
    data = fields.Dict(allow_none=True)

class PaginationSchema(Schema):
    """分页参数模式"""
    page = fields.Integer(missing=1, validate=validate.Range(min=1))
    per_page = fields.Integer(missing=20, validate=validate.Range(min=1, max=100))

class PaginatedResponseSchema(Schema):
    """分页响应模式"""
    items = fields.List(fields.Dict())
    total = fields.Integer()
    page = fields.Integer()
    per_page = fields.Integer()
    pages = fields.Integer()
    has_prev = fields.Boolean()
    has_next = fields.Boolean()
    prev_num = fields.Integer(allow_none=True)
    next_num = fields.Integer(allow_none=True)

# 创建模式实例
user_schema = UserSchema()
users_schema = UserSchema(many=True)
oauth2_client_schema = OAuth2ClientSchema()
oauth2_clients_schema = OAuth2ClientSchema(many=True)
oauth2_token_schema = OAuth2TokenSchema()
oauth2_tokens_schema = OAuth2TokenSchema(many=True)
oauth2_code_schema = OAuth2AuthorizationCodeSchema()
oauth2_codes_schema = OAuth2AuthorizationCodeSchema(many=True)
error_schema = ErrorSchema()
success_schema = SuccessSchema()
pagination_schema = PaginationSchema()
paginated_response_schema = PaginatedResponseSchema()
