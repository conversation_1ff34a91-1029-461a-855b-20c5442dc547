#!/bin/bash

# API 启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Python 版本
check_python() {
    log_info "检查 Python 版本..."
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
        log_success "Python 版本: $PYTHON_VERSION"
    else
        log_error "Python 3 未安装"
        exit 1
    fi
}

# 检查 Poetry
check_poetry() {
    log_info "检查 Poetry..."
    if command -v poetry &> /dev/null; then
        POETRY_VERSION=$(poetry --version | cut -d' ' -f3)
        log_success "Poetry 版本: $POETRY_VERSION"
        return 0
    else
        log_warning "Poetry 未安装"
        return 1
    fi
}

# 安装 Poetry
install_poetry() {
    log_info "安装 Poetry..."
    curl -sSL https://install.python-poetry.org | python3 -
    export PATH="$HOME/.local/bin:$PATH"
    log_success "Poetry 安装完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    if check_poetry; then
        poetry install
        log_success "依赖安装完成"
    else
        log_info "使用 pip 安装依赖..."
        if [ -f "requirements.txt" ]; then
            pip3 install -r requirements.txt
            log_success "依赖安装完成"
        else
            log_error "requirements.txt 文件不存在"
            exit 1
        fi
    fi
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    CONFIG_FILE="../config/config.yaml"
    if [ -f "$CONFIG_FILE" ]; then
        log_success "配置文件存在: $CONFIG_FILE"
    else
        log_warning "配置文件不存在，将使用默认配置"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    if check_poetry; then
        poetry run python migrations/init_db.py init
    else
        python3 migrations/init_db.py init
    fi
    log_success "数据库初始化完成"
}

# 启动应用
start_app() {
    log_info "启动 API 应用..."
    
    # 设置环境变量
    export FLASK_ENV=${FLASK_ENV:-development}
    export FLASK_DEBUG=${FLASK_DEBUG:-1}
    
    if check_poetry; then
        log_info "使用 Poetry 启动应用..."
        poetry run python app.py
    else
        log_info "使用 Python 启动应用..."
        python3 app.py
    fi
}

# 运行测试
run_tests() {
    log_info "运行 API 测试..."
    if check_poetry; then
        poetry run python test_api.py
    else
        python3 test_api.py
    fi
}

# 显示帮助信息
show_help() {
    echo "API 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start         启动应用 (默认)"
    echo "  install       安装依赖"
    echo "  init-db       初始化数据库"
    echo "  test          运行测试"
    echo "  docker        使用 Docker 启动"
    echo "  help          显示帮助信息"
    echo ""
    echo "环境变量:"
    echo "  FLASK_ENV     Flask 环境 (development/production)"
    echo "  FLASK_DEBUG   调试模式 (0/1)"
    echo "  DATABASE_URL  数据库连接 URL"
    echo ""
}

# Docker 启动
start_docker() {
    log_info "使用 Docker 启动应用..."
    
    if command -v docker-compose &> /dev/null; then
        log_info "启动 Docker Compose 服务..."
        docker-compose up -d
        
        log_info "等待服务启动..."
        sleep 10
        
        log_info "检查服务状态..."
        docker-compose ps
        
        log_success "Docker 服务启动完成"
        log_info "API 地址: http://localhost:5000"
        log_info "pgAdmin 地址: http://localhost:8080"
    else
        log_error "docker-compose 未安装"
        exit 1
    fi
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            check_python
            check_config
            start_app
            ;;
        "install")
            check_python
            if ! check_poetry; then
                install_poetry
            fi
            install_dependencies
            ;;
        "init-db")
            check_python
            init_database
            ;;
        "test")
            check_python
            run_tests
            ;;
        "docker")
            start_docker
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
