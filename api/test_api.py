#!/usr/bin/env python3
"""
API 测试脚本
用于验证 API 功能是否正常工作
"""
import requests
import json
import sys
from datetime import datetime

class APITester:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.access_token = None
    
    def log(self, message, level='INFO'):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
    
    def test_health_check(self):
        """测试健康检查"""
        self.log("测试健康检查...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/health")
            if response.status_code == 200:
                data = response.json()
                self.log(f"健康检查通过: {data.get('status')}")
                return True
            else:
                self.log(f"健康检查失败: {response.status_code}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"健康检查异常: {e}", 'ERROR')
            return False
    
    def test_system_info(self):
        """测试系统信息"""
        self.log("测试系统信息...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/info")
            if response.status_code == 200:
                data = response.json()
                self.log(f"系统信息获取成功: {data.get('name')}")
                return True
            else:
                self.log(f"系统信息获取失败: {response.status_code}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"系统信息获取异常: {e}", 'ERROR')
            return False
    
    def test_stats(self):
        """测试统计信息"""
        self.log("测试统计信息...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/stats")
            if response.status_code == 200:
                data = response.json()
                self.log(f"统计信息获取成功: 用户数 {data.get('users', {}).get('total', 0)}")
                return True
            else:
                self.log(f"统计信息获取失败: {response.status_code}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"统计信息获取异常: {e}", 'ERROR')
            return False
    
    def test_config(self):
        """测试配置信息"""
        self.log("测试配置信息...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/config")
            if response.status_code == 200:
                data = response.json()
                self.log(f"配置信息获取成功: 数据库类型 {data.get('database', {}).get('type')}")
                return True
            else:
                self.log(f"配置信息获取失败: {response.status_code}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"配置信息获取异常: {e}", 'ERROR')
            return False
    
    def test_auth_status(self):
        """测试认证状态"""
        self.log("测试认证状态...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/auth/status")
            if response.status_code == 200:
                data = response.json()
                authenticated = data.get('data', {}).get('authenticated', False)
                self.log(f"认证状态获取成功: {'已认证' if authenticated else '未认证'}")
                return True
            else:
                self.log(f"认证状态获取失败: {response.status_code}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"认证状态获取异常: {e}", 'ERROR')
            return False
    
    def test_users_list_unauthorized(self):
        """测试未授权访问用户列表"""
        self.log("测试未授权访问用户列表...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/users")
            if response.status_code == 401:
                self.log("未授权访问正确返回 401")
                return True
            else:
                self.log(f"未授权访问返回错误状态码: {response.status_code}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"未授权访问测试异常: {e}", 'ERROR')
            return False
    
    def test_oauth_clients_unauthorized(self):
        """测试未授权访问 OAuth 客户端列表"""
        self.log("测试未授权访问 OAuth 客户端列表...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/oauth/clients")
            if response.status_code == 401:
                self.log("未授权访问正确返回 401")
                return True
            else:
                self.log(f"未授权访问返回错误状态码: {response.status_code}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"未授权访问测试异常: {e}", 'ERROR')
            return False
    
    def test_invalid_endpoint(self):
        """测试无效端点"""
        self.log("测试无效端点...")
        try:
            response = self.session.get(f"{self.base_url}/api/v1/invalid")
            if response.status_code == 404:
                self.log("无效端点正确返回 404")
                return True
            else:
                self.log(f"无效端点返回错误状态码: {response.status_code}", 'ERROR')
                return False
        except Exception as e:
            self.log(f"无效端点测试异常: {e}", 'ERROR')
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        self.log("开始 API 测试...")
        
        tests = [
            ('健康检查', self.test_health_check),
            ('系统信息', self.test_system_info),
            ('统计信息', self.test_stats),
            ('配置信息', self.test_config),
            ('认证状态', self.test_auth_status),
            ('未授权用户列表', self.test_users_list_unauthorized),
            ('未授权OAuth客户端', self.test_oauth_clients_unauthorized),
            ('无效端点', self.test_invalid_endpoint),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                    self.log(f"✓ {test_name} 通过")
                else:
                    self.log(f"✗ {test_name} 失败", 'ERROR')
            except Exception as e:
                self.log(f"✗ {test_name} 异常: {e}", 'ERROR')
        
        self.log(f"测试完成: {passed}/{total} 通过")
        
        if passed == total:
            self.log("所有测试通过! 🎉")
            return True
        else:
            self.log(f"有 {total - passed} 个测试失败", 'ERROR')
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='API 测试脚本')
    parser.add_argument('--url', default='http://localhost:5000', 
                       help='API 基础 URL (默认: http://localhost:5000)')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='详细输出')
    
    args = parser.parse_args()
    
    tester = APITester(args.url)
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        tester.log("测试被用户中断", 'WARNING')
        sys.exit(1)
    except Exception as e:
        tester.log(f"测试过程中发生异常: {e}", 'ERROR')
        sys.exit(1)

if __name__ == '__main__':
    main()
