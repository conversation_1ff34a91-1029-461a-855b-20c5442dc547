#!/usr/bin/env python3
"""
数据库连接测试脚本
"""
import os
import sys
import psycopg2
from sqlalchemy import create_engine, text
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': '***********',
    'port': 5432,
    'database': 'openauth',
    'user': 'root',
    'password': 'r3j94pf2c'
}

DATABASE_URL = f"postgresql://{DB_CONFIG['user']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"

def log(message, level='INFO'):
    """记录日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {level}: {message}")

def test_psycopg2_connection():
    """使用 psycopg2 测试数据库连接"""
    log("测试 psycopg2 连接...")
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 测试基本查询
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        log(f"PostgreSQL 版本: {version}")
        
        # 测试数据库信息
        cursor.execute("SELECT current_database(), current_user;")
        db_info = cursor.fetchone()
        log(f"当前数据库: {db_info[0]}, 当前用户: {db_info[1]}")
        
        # 检查现有表
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public'
            ORDER BY table_name;
        """)
        tables = cursor.fetchall()
        if tables:
            log(f"现有表: {[table[0] for table in tables]}")
        else:
            log("数据库中没有表")
        
        cursor.close()
        conn.close()
        log("psycopg2 连接测试成功")
        return True
        
    except Exception as e:
        log(f"psycopg2 连接测试失败: {e}", 'ERROR')
        return False

def test_sqlalchemy_connection():
    """使用 SQLAlchemy 测试数据库连接"""
    log("测试 SQLAlchemy 连接...")
    try:
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            # 测试基本查询
            result = conn.execute(text("SELECT version();"))
            version = result.fetchone()[0]
            log(f"PostgreSQL 版本 (SQLAlchemy): {version}")
            
            # 测试数据库信息
            result = conn.execute(text("SELECT current_database(), current_user;"))
            db_info = result.fetchone()
            log(f"当前数据库: {db_info[0]}, 当前用户: {db_info[1]}")
            
            # 检查现有表
            result = conn.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """))
            tables = result.fetchall()
            if tables:
                log(f"现有表: {[table[0] for table in tables]}")
            else:
                log("数据库中没有表")
        
        log("SQLAlchemy 连接测试成功")
        return True
        
    except Exception as e:
        log(f"SQLAlchemy 连接测试失败: {e}", 'ERROR')
        return False

def test_flask_app_connection():
    """测试 Flask 应用的数据库连接"""
    log("测试 Flask 应用数据库连接...")
    try:
        # 添加项目根目录到 Python 路径
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from api import create_app, db
        
        app = create_app()
        
        with app.app_context():
            # 测试数据库连接
            result = db.session.execute(text("SELECT version();"))
            version = result.fetchone()[0]
            log(f"PostgreSQL 版本 (Flask): {version}")
            
            # 测试数据库信息
            result = db.session.execute(text("SELECT current_database(), current_user;"))
            db_info = result.fetchone()
            log(f"当前数据库: {db_info[0]}, 当前用户: {db_info[1]}")
            
            # 检查现有表
            result = db.session.execute(text("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public'
                ORDER BY table_name;
            """))
            tables = result.fetchall()
            if tables:
                log(f"现有表: {[table[0] for table in tables]}")
            else:
                log("数据库中没有表")
        
        log("Flask 应用数据库连接测试成功")
        return True
        
    except Exception as e:
        log(f"Flask 应用数据库连接测试失败: {e}", 'ERROR')
        return False

def check_database_permissions():
    """检查数据库权限"""
    log("检查数据库权限...")
    try:
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 检查用户权限
        cursor.execute("""
            SELECT 
                rolname,
                rolsuper,
                rolcreaterole,
                rolcreatedb,
                rolcanlogin
            FROM pg_roles 
            WHERE rolname = %s;
        """, (DB_CONFIG['user'],))
        
        role_info = cursor.fetchone()
        if role_info:
            log(f"用户权限: 超级用户={role_info[1]}, 创建角色={role_info[2]}, 创建数据库={role_info[3]}, 可登录={role_info[4]}")
        
        # 检查数据库权限
        cursor.execute("""
            SELECT 
                datname,
                datacl
            FROM pg_database 
            WHERE datname = %s;
        """, (DB_CONFIG['database'],))
        
        db_info = cursor.fetchone()
        if db_info:
            log(f"数据库权限: {db_info[1] if db_info[1] else '默认权限'}")
        
        cursor.close()
        conn.close()
        log("数据库权限检查完成")
        return True
        
    except Exception as e:
        log(f"数据库权限检查失败: {e}", 'ERROR')
        return False

def main():
    """主函数"""
    log("开始数据库连接测试...")
    log(f"数据库配置: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
    
    tests = [
        ("psycopg2 连接", test_psycopg2_connection),
        ("SQLAlchemy 连接", test_sqlalchemy_connection),
        ("数据库权限", check_database_permissions),
        ("Flask 应用连接", test_flask_app_connection),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                log(f"✓ {test_name} 通过")
            else:
                log(f"✗ {test_name} 失败", 'ERROR')
        except Exception as e:
            log(f"✗ {test_name} 异常: {e}", 'ERROR')
        
        print("-" * 50)
    
    log(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        log("所有数据库连接测试通过! 🎉")
        return True
    else:
        log(f"有 {total - passed} 个测试失败", 'ERROR')
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log("测试被用户中断", 'WARNING')
        sys.exit(1)
    except Exception as e:
        log(f"测试过程中发生异常: {e}", 'ERROR')
        sys.exit(1)
