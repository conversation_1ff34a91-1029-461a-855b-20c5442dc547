"""
API 工具函数
"""
from functools import wraps
from flask import request, jsonify, session
from marshmallow import ValidationError
from app.models.user import User
from app.models.oauth2 import OAuth2Token
from .schemas import error_schema
import secrets
import time

def validate_json(schema):
    """
    JSON 数据验证装饰器
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 验证请求数据
                json_data = request.get_json()
                if json_data is None:
                    return jsonify(error_schema.dump({
                        'error': 'invalid_request',
                        'message': '请求必须包含有效的 JSON 数据'
                    })), 400
                
                # 使用模式验证数据
                validated_data = schema.load(json_data)
                request.validated_data = validated_data
                
                return f(*args, **kwargs)
            except ValidationError as err:
                return jsonify(error_schema.dump({
                    'error': 'validation_error',
                    'message': '数据验证失败',
                    'details': err.messages
                })), 400
            except Exception as err:
                return jsonify(error_schema.dump({
                    'error': 'internal_error',
                    'message': str(err)
                })), 500
        
        return decorated_function
    return decorator

def require_auth(f):
    """
    需要认证的装饰器
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 检查会话认证
        if 'user_id' in session:
            user = User.query.get(session['user_id'])
            if user:
                request.current_user = user
                return f(*args, **kwargs)
        
        # 检查 Bearer Token 认证
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            oauth_token = OAuth2Token.query.filter_by(access_token=token).first()
            if oauth_token and oauth_token.is_access_token_active():
                user = User.query.get(oauth_token.user_id)
                if user:
                    request.current_user = user
                    request.oauth_token = oauth_token
                    return f(*args, **kwargs)
        
        return jsonify(error_schema.dump({
            'error': 'unauthorized',
            'message': '需要认证才能访问此资源'
        })), 401
    
    return decorated_function

def require_admin(f):
    """
    需要管理员权限的装饰器
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not hasattr(request, 'current_user'):
            return jsonify(error_schema.dump({
                'error': 'unauthorized',
                'message': '需要认证才能访问此资源'
            })), 401
        
        # 简化处理：检查用户是否为管理员
        # 实际应用中应该有更复杂的权限系统
        user = request.current_user
        if not user.saml_attributes or not user.saml_attributes.get('admin', False):
            return jsonify(error_schema.dump({
                'error': 'forbidden',
                'message': '需要管理员权限才能访问此资源'
            })), 403
        
        return f(*args, **kwargs)
    
    return decorated_function

def paginate_query(query, page=1, per_page=20):
    """
    分页查询工具函数
    """
    try:
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return {
            'items': pagination.items,
            'total': pagination.total,
            'page': pagination.page,
            'per_page': pagination.per_page,
            'pages': pagination.pages,
            'has_prev': pagination.has_prev,
            'has_next': pagination.has_next,
            'prev_num': pagination.prev_num,
            'next_num': pagination.next_num
        }
    except Exception as e:
        return None

def generate_client_credentials():
    """
    生成 OAuth2 客户端凭证
    """
    client_id = secrets.token_urlsafe(32)
    client_secret = secrets.token_urlsafe(48)
    return client_id, client_secret

def generate_authorization_code():
    """
    生成授权码
    """
    return secrets.token_urlsafe(32)

def generate_access_token():
    """
    生成访问令牌
    """
    return secrets.token_urlsafe(48)

def generate_refresh_token():
    """
    生成刷新令牌
    """
    return secrets.token_urlsafe(48)

def current_timestamp():
    """
    获取当前时间戳
    """
    return int(time.time())

def handle_api_error(error, message, code=400):
    """
    统一的 API 错误处理
    """
    return jsonify(error_schema.dump({
        'error': error,
        'message': message,
        'code': code
    })), code

def success_response(message, data=None):
    """
    统一的成功响应
    """
    from .schemas import success_schema
    return jsonify(success_schema.dump({
        'success': True,
        'message': message,
        'data': data
    }))

def validate_pagination_params():
    """
    验证分页参数
    """
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        if page < 1:
            page = 1
        if per_page < 1 or per_page > 100:
            per_page = 20
            
        return page, per_page
    except ValueError:
        return 1, 20
