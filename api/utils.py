"""
API 工具函数 (已废弃)
此文件已被重构为模块化的工具结构，请使用 utils/ 目录下的文件
"""

# 此文件保留用于向后兼容，实际功能已迁移到以下文件：
# - utils/auth.py: 认证和授权相关
# - utils/pagination.py: 分页相关
# - utils/response.py: 响应格式化
# - utils/validation.py: 数据验证
# - utils/security.py: 安全相关

# 为了向后兼容，重新导入新的工具函数
from .utils.auth import require_auth, require_admin, get_current_user
from .utils.pagination import paginate_query, validate_pagination_params
from .utils.response import success_response, error_response as handle_api_error
from .utils.validation import validate_json
from .utils.security import (
    generate_client_credentials,
    generate_authorization_code,
    generate_access_token,
    generate_refresh_token,
    current_timestamp
)

# 废弃通知
import warnings
warnings.warn(
    "api.utils 模块已废弃，请直接从 api.utils.* 子模块导入所需功能",
    DeprecationWarning,
    stacklevel=2
)
