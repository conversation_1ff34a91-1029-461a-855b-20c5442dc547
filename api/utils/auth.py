"""
认证和授权工具函数
"""
from functools import wraps
from flask import request, session
from api.models import User, OAuth2Token
from api.utils.response import unauthorized_response, forbidden_response

def get_current_user():
    """
    获取当前用户
    
    Returns:
        User: 当前用户对象，如果未认证则返回 None
    """
    # 检查会话认证
    if 'user_id' in session:
        user = User.query.get(session['user_id'])
        if user and user.is_active:
            return user
    
    # 检查 Bearer Token 认证
    auth_header = request.headers.get('Authorization')
    if auth_header and auth_header.startswith('Bearer '):
        token = auth_header.split(' ')[1]
        oauth_token = OAuth2Token.find_by_access_token(token)
        if oauth_token and oauth_token.is_access_token_active():
            user = User.query.get(oauth_token.user_id)
            if user and user.is_active:
                return user
    
    return None

def require_auth(f):
    """
    需要认证的装饰器
    
    Args:
        f: 被装饰的函数
    
    Returns:
        function: 装饰后的函数
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            return unauthorized_response()
        
        # 将当前用户添加到请求上下文
        request.current_user = user
        
        # 如果是通过 OAuth Token 认证，也添加 token 信息
        auth_header = request.headers.get('Authorization')
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            oauth_token = OAuth2Token.find_by_access_token(token)
            if oauth_token:
                request.oauth_token = oauth_token
        
        return f(*args, **kwargs)
    
    return decorated_function

def require_admin(f):
    """
    需要管理员权限的装饰器
    
    Args:
        f: 被装饰的函数
    
    Returns:
        function: 装饰后的函数
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if not user:
            return unauthorized_response()
        
        if not user.is_admin:
            return forbidden_response("需要管理员权限才能访问此资源")
        
        # 将当前用户添加到请求上下文
        request.current_user = user
        
        return f(*args, **kwargs)
    
    return decorated_function

def require_self_or_admin(user_id_param='user_id'):
    """
    需要是用户本人或管理员权限的装饰器
    
    Args:
        user_id_param (str): URL 参数中用户 ID 的参数名
    
    Returns:
        function: 装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = get_current_user()
            if not user:
                return unauthorized_response()
            
            # 获取 URL 参数中的用户 ID
            target_user_id = kwargs.get(user_id_param)
            if target_user_id is None:
                target_user_id = request.view_args.get(user_id_param)
            
            # 检查是否是用户本人或管理员
            if user.id != int(target_user_id) and not user.is_admin:
                return forbidden_response("只能访问自己的资源或需要管理员权限")
            
            # 将当前用户添加到请求上下文
            request.current_user = user
            
            return f(*args, **kwargs)
        
        return decorated_function
    return decorator

def check_scope(required_scope):
    """
    检查 OAuth 令牌是否具有所需的作用域
    
    Args:
        required_scope (str): 所需的作用域
    
    Returns:
        function: 装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 首先检查用户是否已认证
            user = get_current_user()
            if not user:
                return unauthorized_response()
            
            # 如果是通过会话认证，跳过作用域检查
            if 'user_id' in session:
                request.current_user = user
                return f(*args, **kwargs)
            
            # 检查 OAuth 令牌的作用域
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                oauth_token = OAuth2Token.find_by_access_token(token)
                if oauth_token and oauth_token.is_access_token_active():
                    token_scopes = oauth_token.scope.split() if oauth_token.scope else []
                    if required_scope not in token_scopes:
                        return forbidden_response(f"令牌缺少所需的作用域: {required_scope}")
                    
                    request.current_user = user
                    request.oauth_token = oauth_token
                    return f(*args, **kwargs)
            
            return unauthorized_response()
        
        return decorated_function
    return decorator

def optional_auth(f):
    """
    可选认证的装饰器（认证失败不会返回错误）
    
    Args:
        f: 被装饰的函数
    
    Returns:
        function: 装饰后的函数
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = get_current_user()
        if user:
            request.current_user = user
            
            # 如果是通过 OAuth Token 认证，也添加 token 信息
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                oauth_token = OAuth2Token.find_by_access_token(token)
                if oauth_token:
                    request.oauth_token = oauth_token
        
        return f(*args, **kwargs)
    
    return decorated_function
