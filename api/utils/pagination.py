"""
分页工具函数
"""
from flask import request

def validate_pagination_params():
    """
    验证分页参数
    
    Returns:
        tuple: (page, per_page) 页码和每页数量
    """
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 20))
        
        # 验证参数范围
        if page < 1:
            page = 1
        if per_page < 1:
            per_page = 20
        elif per_page > 100:  # 限制最大每页数量
            per_page = 100
            
        return page, per_page
    except (ValueError, TypeError):
        return 1, 20

def paginate_query(query, page=None, per_page=None):
    """
    分页查询工具函数
    
    Args:
        query: SQLAlchemy 查询对象
        page (int, optional): 页码
        per_page (int, optional): 每页数量
    
    Returns:
        dict: 分页结果，包含数据和分页信息
    """
    if page is None or per_page is None:
        page, per_page = validate_pagination_params()
    
    try:
        pagination = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return {
            'items': pagination.items,
            'pagination': {
                'total': pagination.total,
                'page': pagination.page,
                'per_page': pagination.per_page,
                'pages': pagination.pages,
                'has_prev': pagination.has_prev,
                'has_next': pagination.has_next,
                'prev_num': pagination.prev_num,
                'next_num': pagination.next_num
            }
        }
    except Exception as e:
        return {
            'items': [],
            'pagination': {
                'total': 0,
                'page': page,
                'per_page': per_page,
                'pages': 0,
                'has_prev': False,
                'has_next': False,
                'prev_num': None,
                'next_num': None
            }
        }

def get_sort_params(allowed_fields=None):
    """
    获取排序参数
    
    Args:
        allowed_fields (list, optional): 允许排序的字段列表
    
    Returns:
        tuple: (sort_field, sort_order) 排序字段和排序方向
    """
    sort_field = request.args.get('sort', 'id')
    sort_order = request.args.get('order', 'asc').lower()
    
    # 验证排序方向
    if sort_order not in ['asc', 'desc']:
        sort_order = 'asc'
    
    # 验证排序字段
    if allowed_fields and sort_field not in allowed_fields:
        sort_field = allowed_fields[0] if allowed_fields else 'id'
    
    return sort_field, sort_order

def apply_sorting(query, model, sort_field='id', sort_order='asc'):
    """
    应用排序到查询
    
    Args:
        query: SQLAlchemy 查询对象
        model: 数据模型类
        sort_field (str): 排序字段
        sort_order (str): 排序方向 ('asc' 或 'desc')
    
    Returns:
        query: 应用排序后的查询对象
    """
    try:
        # 获取模型的属性
        if hasattr(model, sort_field):
            field = getattr(model, sort_field)
            if sort_order == 'desc':
                query = query.order_by(field.desc())
            else:
                query = query.order_by(field.asc())
        else:
            # 如果字段不存在，使用默认排序
            query = query.order_by(model.id.asc())
    except Exception:
        # 如果排序失败，使用默认排序
        query = query.order_by(model.id.asc())
    
    return query

def get_filter_params():
    """
    获取过滤参数
    
    Returns:
        dict: 过滤参数字典
    """
    filters = {}
    
    # 获取所有查询参数，排除分页和排序参数
    excluded_params = ['page', 'per_page', 'sort', 'order']
    
    for key, value in request.args.items():
        if key not in excluded_params and value:
            filters[key] = value
    
    return filters

def apply_filters(query, model, filters):
    """
    应用过滤条件到查询
    
    Args:
        query: SQLAlchemy 查询对象
        model: 数据模型类
        filters (dict): 过滤条件字典
    
    Returns:
        query: 应用过滤后的查询对象
    """
    for field, value in filters.items():
        if hasattr(model, field):
            try:
                # 简单的等值过滤
                query = query.filter(getattr(model, field) == value)
            except Exception:
                # 如果过滤失败，忽略该条件
                continue
    
    return query

def paginate_with_filters_and_sorting(query, model, allowed_sort_fields=None):
    """
    综合分页、过滤和排序功能
    
    Args:
        query: SQLAlchemy 查询对象
        model: 数据模型类
        allowed_sort_fields (list, optional): 允许排序的字段列表
    
    Returns:
        dict: 包含数据和分页信息的结果
    """
    # 获取过滤参数并应用
    filters = get_filter_params()
    query = apply_filters(query, model, filters)
    
    # 获取排序参数并应用
    sort_field, sort_order = get_sort_params(allowed_sort_fields)
    query = apply_sorting(query, model, sort_field, sort_order)
    
    # 获取分页参数并应用
    page, per_page = validate_pagination_params()
    result = paginate_query(query, page, per_page)
    
    # 添加过滤和排序信息到结果中
    result['filters'] = filters
    result['sorting'] = {
        'field': sort_field,
        'order': sort_order
    }
    
    return result
