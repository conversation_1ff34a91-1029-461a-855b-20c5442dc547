"""
响应工具函数
"""
from flask import jsonify

def success_response(message, data=None, status_code=200):
    """
    统一的成功响应格式
    
    Args:
        message (str): 成功消息
        data (dict, optional): 响应数据
        status_code (int): HTTP 状态码
    
    Returns:
        tuple: (response, status_code)
    """
    response_data = {
        'success': True,
        'message': message
    }
    
    if data is not None:
        response_data['data'] = data
    
    return jsonify(response_data), status_code

def error_response(error_type, message, status_code=400, details=None):
    """
    统一的错误响应格式
    
    Args:
        error_type (str): 错误类型
        message (str): 错误消息
        status_code (int): HTTP 状态码
        details (dict, optional): 错误详情
    
    Returns:
        tuple: (response, status_code)
    """
    response_data = {
        'success': False,
        'error': error_type,
        'message': message
    }
    
    if details is not None:
        response_data['details'] = details
    
    return jsonify(response_data), status_code

def paginated_response(items, pagination_info, message="数据获取成功"):
    """
    分页响应格式
    
    Args:
        items (list): 数据项列表
        pagination_info (dict): 分页信息
        message (str): 响应消息
    
    Returns:
        tuple: (response, status_code)
    """
    response_data = {
        'success': True,
        'message': message,
        'data': {
            'items': items,
            'pagination': pagination_info
        }
    }
    
    return jsonify(response_data), 200

def validation_error_response(errors):
    """
    数据验证错误响应
    
    Args:
        errors (dict): 验证错误信息
    
    Returns:
        tuple: (response, status_code)
    """
    return error_response(
        error_type='validation_error',
        message='数据验证失败',
        status_code=400,
        details=errors
    )

def not_found_response(resource_type="资源"):
    """
    资源未找到响应
    
    Args:
        resource_type (str): 资源类型
    
    Returns:
        tuple: (response, status_code)
    """
    return error_response(
        error_type='not_found',
        message=f'{resource_type}不存在',
        status_code=404
    )

def unauthorized_response(message="需要认证才能访问此资源"):
    """
    未授权响应
    
    Args:
        message (str): 错误消息
    
    Returns:
        tuple: (response, status_code)
    """
    return error_response(
        error_type='unauthorized',
        message=message,
        status_code=401
    )

def forbidden_response(message="没有权限访问此资源"):
    """
    禁止访问响应
    
    Args:
        message (str): 错误消息
    
    Returns:
        tuple: (response, status_code)
    """
    return error_response(
        error_type='forbidden',
        message=message,
        status_code=403
    )

def conflict_response(message="资源冲突"):
    """
    资源冲突响应
    
    Args:
        message (str): 错误消息
    
    Returns:
        tuple: (response, status_code)
    """
    return error_response(
        error_type='conflict',
        message=message,
        status_code=409
    )

def internal_error_response(message="服务器内部错误"):
    """
    服务器内部错误响应
    
    Args:
        message (str): 错误消息
    
    Returns:
        tuple: (response, status_code)
    """
    return error_response(
        error_type='internal_server_error',
        message=message,
        status_code=500
    )
