"""
安全相关工具函数
"""
import secrets
import time
import hashlib
import base64
from datetime import datetime, timed<PERSON><PERSON>

def generate_client_credentials():
    """
    生成 OAuth2 客户端凭证
    
    Returns:
        tuple: (client_id, client_secret)
    """
    client_id = secrets.token_urlsafe(32)
    client_secret = secrets.token_urlsafe(48)
    return client_id, client_secret

def generate_authorization_code():
    """
    生成授权码
    
    Returns:
        str: 授权码
    """
    return secrets.token_urlsafe(32)

def generate_access_token():
    """
    生成访问令牌
    
    Returns:
        str: 访问令牌
    """
    return secrets.token_urlsafe(48)

def generate_refresh_token():
    """
    生成刷新令牌
    
    Returns:
        str: 刷新令牌
    """
    return secrets.token_urlsafe(48)

def generate_tokens(expires_in=3600):
    """
    生成访问令牌和刷新令牌
    
    Args:
        expires_in (int): 访问令牌有效期（秒）
    
    Returns:
        dict: 包含令牌信息的字典
    """
    access_token = generate_access_token()
    refresh_token = generate_refresh_token()
    issued_at = int(time.time())
    
    return {
        'access_token': access_token,
        'refresh_token': refresh_token,
        'token_type': 'Bearer',
        'expires_in': expires_in,
        'issued_at': issued_at
    }

def generate_state():
    """
    生成 OAuth 状态参数
    
    Returns:
        str: 状态参数
    """
    return secrets.token_urlsafe(32)

def generate_nonce():
    """
    生成 OIDC nonce 参数
    
    Returns:
        str: nonce 参数
    """
    return secrets.token_urlsafe(32)

def hash_password(password):
    """
    哈希密码（如果需要本地密码认证）
    
    Args:
        password (str): 明文密码
    
    Returns:
        str: 哈希后的密码
    """
    # 使用 PBKDF2 进行密码哈希
    salt = secrets.token_bytes(32)
    pwdhash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
    return base64.b64encode(salt + pwdhash).decode('ascii')

def verify_password(stored_password, provided_password):
    """
    验证密码
    
    Args:
        stored_password (str): 存储的哈希密码
        provided_password (str): 提供的明文密码
    
    Returns:
        bool: 密码是否匹配
    """
    try:
        stored_bytes = base64.b64decode(stored_password.encode('ascii'))
        salt = stored_bytes[:32]
        stored_hash = stored_bytes[32:]
        pwdhash = hashlib.pbkdf2_hmac('sha256', provided_password.encode('utf-8'), salt, 100000)
        return secrets.compare_digest(stored_hash, pwdhash)
    except Exception:
        return False

def generate_api_key():
    """
    生成 API 密钥
    
    Returns:
        str: API 密钥
    """
    return secrets.token_urlsafe(64)

def validate_pkce_challenge(code_verifier, code_challenge, code_challenge_method='S256'):
    """
    验证 PKCE 挑战
    
    Args:
        code_verifier (str): 代码验证器
        code_challenge (str): 代码挑战
        code_challenge_method (str): 挑战方法
    
    Returns:
        bool: 验证是否通过
    """
    if code_challenge_method == 'S256':
        # SHA256 方法
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        expected_challenge = base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')
        return secrets.compare_digest(expected_challenge, code_challenge)
    elif code_challenge_method == 'plain':
        # 明文方法
        return secrets.compare_digest(code_verifier, code_challenge)
    else:
        return False

def generate_pkce_pair():
    """
    生成 PKCE 代码验证器和挑战对
    
    Returns:
        tuple: (code_verifier, code_challenge)
    """
    code_verifier = base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')
    digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
    code_challenge = base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')
    return code_verifier, code_challenge

def is_token_expired(issued_at, expires_in):
    """
    检查令牌是否过期
    
    Args:
        issued_at (int): 令牌签发时间戳
        expires_in (int): 有效期（秒）
    
    Returns:
        bool: 是否过期
    """
    current_time = int(time.time())
    expiration_time = issued_at + expires_in
    return current_time >= expiration_time

def get_token_remaining_time(issued_at, expires_in):
    """
    获取令牌剩余有效时间
    
    Args:
        issued_at (int): 令牌签发时间戳
        expires_in (int): 有效期（秒）
    
    Returns:
        int: 剩余时间（秒），如果已过期返回 0
    """
    current_time = int(time.time())
    expiration_time = issued_at + expires_in
    remaining = expiration_time - current_time
    return max(0, remaining)

def sanitize_redirect_uri(uri):
    """
    清理重定向 URI
    
    Args:
        uri (str): 原始 URI
    
    Returns:
        str: 清理后的 URI
    """
    # 移除可能的恶意字符
    import urllib.parse
    parsed = urllib.parse.urlparse(uri)
    
    # 重新构建 URI，确保安全
    sanitized = urllib.parse.urlunparse((
        parsed.scheme,
        parsed.netloc,
        parsed.path,
        parsed.params,
        parsed.query,
        ''  # 移除 fragment
    ))
    
    return sanitized

def generate_csrf_token():
    """
    生成 CSRF 令牌
    
    Returns:
        str: CSRF 令牌
    """
    return secrets.token_urlsafe(32)

def constant_time_compare(a, b):
    """
    常量时间字符串比较（防止时序攻击）
    
    Args:
        a (str): 字符串 A
        b (str): 字符串 B
    
    Returns:
        bool: 是否相等
    """
    return secrets.compare_digest(a, b)
