"""
数据验证工具函数
"""
from functools import wraps
from flask import request
from marshmallow import ValidationError
from api.utils.response import validation_error_response, error_response

def validate_json(schema):
    """
    JSON 数据验证装饰器
    
    Args:
        schema: Marshmallow 模式对象
    
    Returns:
        function: 装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 检查请求是否包含 JSON 数据
                if not request.is_json:
                    return error_response(
                        'invalid_request',
                        '请求必须包含有效的 JSON 数据',
                        400
                    )
                
                json_data = request.get_json()
                if json_data is None:
                    return error_response(
                        'invalid_request',
                        '请求体不能为空',
                        400
                    )
                
                # 使用模式验证数据
                validated_data = schema.load(json_data)
                request.validated_data = validated_data
                
                return f(*args, **kwargs)
                
            except ValidationError as err:
                return validation_error_response(err.messages)
            except Exception as err:
                return error_response(
                    'internal_error',
                    f'数据验证时发生错误: {str(err)}',
                    500
                )
        
        return decorated_function
    return decorator

def validate_query_params(schema):
    """
    查询参数验证装饰器
    
    Args:
        schema: Marshmallow 模式对象
    
    Returns:
        function: 装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 验证查询参数
                validated_data = schema.load(request.args)
                request.validated_params = validated_data
                
                return f(*args, **kwargs)
                
            except ValidationError as err:
                return validation_error_response(err.messages)
            except Exception as err:
                return error_response(
                    'internal_error',
                    f'参数验证时发生错误: {str(err)}',
                    500
                )
        
        return decorated_function
    return decorator

def validate_path_params(schema):
    """
    路径参数验证装饰器
    
    Args:
        schema: Marshmallow 模式对象
    
    Returns:
        function: 装饰器函数
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            try:
                # 验证路径参数
                validated_data = schema.load(request.view_args)
                request.validated_path = validated_data
                
                return f(*args, **kwargs)
                
            except ValidationError as err:
                return validation_error_response(err.messages)
            except Exception as err:
                return error_response(
                    'internal_error',
                    f'路径参数验证时发生错误: {str(err)}',
                    500
                )
        
        return decorated_function
    return decorator

def validate_email(email):
    """
    验证邮箱格式
    
    Args:
        email (str): 邮箱地址
    
    Returns:
        bool: 是否为有效邮箱
    """
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_username(username):
    """
    验证用户名格式
    
    Args:
        username (str): 用户名
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if not username:
        return False, "用户名不能为空"
    
    if len(username) < 3:
        return False, "用户名长度不能少于3个字符"
    
    if len(username) > 80:
        return False, "用户名长度不能超过80个字符"
    
    import re
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        return False, "用户名只能包含字母、数字、下划线和连字符"
    
    return True, None

def validate_url(url):
    """
    验证 URL 格式
    
    Args:
        url (str): URL 地址
    
    Returns:
        bool: 是否为有效 URL
    """
    import re
    pattern = r'^https?://(?:[-\w.])+(?:\:[0-9]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:\#(?:[\w.])*)?)?$'
    return re.match(pattern, url) is not None

def validate_client_id(client_id):
    """
    验证客户端 ID 格式
    
    Args:
        client_id (str): 客户端 ID
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if not client_id:
        return False, "客户端 ID 不能为空"
    
    if len(client_id) < 10:
        return False, "客户端 ID 长度不能少于10个字符"
    
    if len(client_id) > 48:
        return False, "客户端 ID 长度不能超过48个字符"
    
    import re
    if not re.match(r'^[a-zA-Z0-9_-]+$', client_id):
        return False, "客户端 ID 只能包含字母、数字、下划线和连字符"
    
    return True, None

def validate_scope(scope):
    """
    验证 OAuth 作用域格式
    
    Args:
        scope (str): 作用域字符串
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if not scope:
        return False, "作用域不能为空"
    
    # 作用域应该是空格分隔的字符串
    scopes = scope.split()
    
    for s in scopes:
        if not re.match(r'^[a-zA-Z0-9_:-]+$', s):
            return False, f"无效的作用域格式: {s}"
    
    return True, None

def validate_redirect_uris(redirect_uris):
    """
    验证重定向 URI 列表
    
    Args:
        redirect_uris (list): 重定向 URI 列表
    
    Returns:
        tuple: (is_valid, error_message)
    """
    if not redirect_uris:
        return False, "重定向 URI 列表不能为空"
    
    if not isinstance(redirect_uris, list):
        return False, "重定向 URI 必须是列表格式"
    
    for uri in redirect_uris:
        if not validate_url(uri):
            return False, f"无效的重定向 URI: {uri}"
    
    return True, None
