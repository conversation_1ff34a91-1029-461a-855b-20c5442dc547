#!/usr/bin/env python3
"""
配置验证脚本
验证当前配置是否正确
"""
import os
import sys
from datetime import datetime

def log(message, level='INFO'):
    """记录日志"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    print(f"[{timestamp}] {level}: {message}")

def verify_config_file():
    """验证配置文件"""
    log("验证配置文件...")
    
    config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config', 'config.yaml')
    
    if not os.path.exists(config_path):
        log(f"配置文件不存在: {config_path}", 'ERROR')
        return False
    
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 验证数据库配置
        db_url = config.get('database', {}).get('url', '')
        if '********************************************/openauth' in db_url:
            log("✓ 数据库配置正确")
        else:
            log(f"✗ 数据库配置不正确: {db_url}", 'ERROR')
            return False
        
        # 验证其他配置
        if config.get('saml'):
            log("✓ SAML 配置存在")
        else:
            log("✗ SAML 配置缺失", 'WARNING')
        
        if config.get('oidc'):
            log("✓ OIDC 配置存在")
        else:
            log("✗ OIDC 配置缺失", 'WARNING')
        
        if config.get('redis'):
            log("✓ Redis 配置存在")
        else:
            log("✗ Redis 配置缺失", 'WARNING')
        
        log("配置文件验证完成")
        return True
        
    except Exception as e:
        log(f"配置文件验证失败: {e}", 'ERROR')
        return False

def verify_dependencies():
    """验证依赖包"""
    log("验证依赖包...")
    
    required_packages = [
        'flask',
        'sqlalchemy',
        'psycopg2',
        'marshmallow',
        'redis',
        'yaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'yaml':
                import yaml
            elif package == 'psycopg2':
                import psycopg2
            else:
                __import__(package)
            log(f"✓ {package} 已安装")
        except ImportError:
            log(f"✗ {package} 未安装", 'ERROR')
            missing_packages.append(package)
    
    if missing_packages:
        log(f"缺失依赖包: {missing_packages}", 'ERROR')
        return False
    else:
        log("所有依赖包已安装")
        return True

def verify_environment():
    """验证环境变量"""
    log("验证环境变量...")
    
    # 检查可选的环境变量
    env_vars = [
        'DATABASE_URL',
        'SECRET_KEY',
        'FLASK_ENV',
        'REDIS_HOST'
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            if var == 'DATABASE_URL':
                log(f"✓ {var} = {value}")
            else:
                log(f"✓ {var} = {value[:20]}..." if len(value) > 20 else f"✓ {var} = {value}")
        else:
            log(f"- {var} 未设置 (将使用默认值)")
    
    return True

def verify_file_structure():
    """验证文件结构"""
    log("验证文件结构...")
    
    required_files = [
        'app.py',
        'config.py',
        '__init__.py',
        'models/__init__.py',
        'models/user.py',
        'models/oauth2.py',
        'routes/__init__.py',
        'routes/health.py',
        'routes/users.py',
        'routes/oauth.py',
        'routes/auth.py',
        'utils/__init__.py',
        'utils/auth.py',
        'utils/response.py',
        'migrations/init_db.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        full_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), file_path)
        if os.path.exists(full_path):
            log(f"✓ {file_path}")
        else:
            log(f"✗ {file_path} 缺失", 'ERROR')
            missing_files.append(file_path)
    
    if missing_files:
        log(f"缺失文件: {missing_files}", 'ERROR')
        return False
    else:
        log("文件结构完整")
        return True

def main():
    """主函数"""
    log("开始配置验证...")
    
    tests = [
        ("配置文件", verify_config_file),
        ("依赖包", verify_dependencies),
        ("环境变量", verify_environment),
        ("文件结构", verify_file_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                log(f"✓ {test_name} 验证通过")
            else:
                log(f"✗ {test_name} 验证失败", 'ERROR')
        except Exception as e:
            log(f"✗ {test_name} 验证异常: {e}", 'ERROR')
        
        print("-" * 50)
    
    log(f"验证完成: {passed}/{total} 通过")
    
    if passed == total:
        log("所有配置验证通过! 🎉")
        log("可以运行以下命令启动应用:")
        log("  ./start.sh test-db    # 测试数据库连接")
        log("  ./start.sh init-db    # 初始化数据库")
        log("  ./start.sh start      # 启动应用")
        return True
    else:
        log(f"有 {total - passed} 个验证失败", 'ERROR')
        log("请修复上述问题后重新运行验证")
        return False

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        log("验证被用户中断", 'WARNING')
        sys.exit(1)
    except Exception as e:
        log(f"验证过程中发生异常: {e}", 'ERROR')
        sys.exit(1)
