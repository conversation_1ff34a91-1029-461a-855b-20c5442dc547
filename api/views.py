"""
API 视图模块 (已废弃)
此文件已被重构为模块化的路由结构，请使用 routes/ 目录下的文件
"""

# 此文件保留用于向后兼容，实际功能已迁移到以下文件：
# - routes/health.py: 健康检查和系统信息
# - routes/users.py: 用户管理
# - routes/oauth.py: OAuth2 管理
# - routes/auth.py: 认证相关

from flask import jsonify

def deprecated_notice():
    """
    废弃通知
    """
    return jsonify({
        'message': '此端点已废弃，请使用新的模块化 API',
        'new_endpoints': {
            'health': '/api/v1/health',
            'info': '/api/v1/info',
            'stats': '/api/v1/stats',
            'config': '/api/v1/config',
            'users': '/api/v1/users',
            'oauth': '/api/v1/oauth',
            'auth': '/api/v1/auth'
        }
    }), 410
