"""
API 主要视图模块
提供系统信息和健康检查等基础接口
"""
from flask import jsonify, request
from . import api_bp
from .utils import success_response, handle_api_error
from .schemas import error_schema
from app import db
import os
import sys
from datetime import datetime

@api_bp.route('/health', methods=['GET'])
def health_check():
    """
    健康检查端点
    """
    try:
        # 检查数据库连接
        db.session.execute('SELECT 1')
        db_status = 'healthy'
    except Exception as e:
        db_status = f'error: {str(e)}'
    
    # 检查 Redis 连接（如果配置了）
    redis_status = 'not_configured'
    try:
        import redis
        from config import load_config
        config = load_config()
        redis_config = config.get('redis', {})
        if redis_config:
            r = redis.Redis(
                host=redis_config.get('host', 'localhost'),
                port=redis_config.get('port', 6379),
                db=redis_config.get('db', 0)
            )
            r.ping()
            redis_status = 'healthy'
    except Exception as e:
        redis_status = f'error: {str(e)}'
    
    health_data = {
        'status': 'healthy' if db_status == 'healthy' else 'unhealthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0',
        'services': {
            'database': db_status,
            'redis': redis_status
        },
        'system': {
            'python_version': sys.version,
            'platform': sys.platform
        }
    }
    
    status_code = 200 if health_data['status'] == 'healthy' else 503
    return jsonify(health_data), status_code

@api_bp.route('/info', methods=['GET'])
def system_info():
    """
    系统信息端点
    """
    try:
        from config import load_config
        config = load_config()
        
        info_data = {
            'name': 'SAML2 SP + OIDC IdP System',
            'description': '基于 Flask 的 SAML2 服务提供者和 OIDC 身份提供者系统',
            'version': '1.0.0',
            'api_version': 'v1',
            'endpoints': {
                'saml': {
                    'login': '/saml/login',
                    'acs': '/saml/acs',
                    'metadata': '/saml/metadata'
                },
                'oidc': {
                    'authorize': '/oauth/authorize',
                    'token': '/oauth/token',
                    'userinfo': '/oauth/userinfo',
                    'jwks': '/oauth/jwks',
                    'discovery': '/.well-known/openid-configuration'
                },
                'api': {
                    'base': '/api/v1',
                    'health': '/api/v1/health',
                    'info': '/api/v1/info',
                    'users': '/api/v1/users',
                    'oauth_clients': '/api/v1/oauth/clients',
                    'oauth_tokens': '/api/v1/oauth/tokens'
                }
            },
            'features': [
                'SAML2 Service Provider',
                'OpenID Connect Identity Provider',
                'OAuth2 Authorization Server',
                'User Management',
                'Session Management',
                'RESTful API'
            ],
            'supported_flows': [
                'SAML2 Web SSO',
                'OAuth2 Authorization Code',
                'OpenID Connect Authorization Code'
            ]
        }
        
        return jsonify(info_data)
    except Exception as e:
        return handle_api_error('system_error', f'获取系统信息失败: {str(e)}', 500)

@api_bp.route('/stats', methods=['GET'])
def system_stats():
    """
    系统统计信息端点
    """
    try:
        from app.models.user import User
        from app.models.oauth2 import OAuth2Client, OAuth2Token, OAuth2AuthorizationCode
        
        # 统计用户数量
        total_users = User.query.count()
        active_users = User.query.filter(User.last_login.isnot(None)).count()
        
        # 统计 OAuth2 客户端数量
        total_clients = OAuth2Client.query.count()
        
        # 统计令牌数量
        total_tokens = OAuth2Token.query.count()
        active_tokens = OAuth2Token.query.filter_by(revoked=0).count()
        
        # 统计授权码数量
        total_codes = OAuth2AuthorizationCode.query.count()
        
        stats_data = {
            'users': {
                'total': total_users,
                'active': active_users
            },
            'oauth2': {
                'clients': total_clients,
                'tokens': {
                    'total': total_tokens,
                    'active': active_tokens
                },
                'authorization_codes': total_codes
            },
            'timestamp': datetime.utcnow().isoformat()
        }
        
        return jsonify(stats_data)
    except Exception as e:
        return handle_api_error('system_error', f'获取统计信息失败: {str(e)}', 500)

@api_bp.route('/config', methods=['GET'])
def system_config():
    """
    系统配置信息端点（仅返回公开配置）
    """
    try:
        from config import load_config
        config = load_config()
        
        # 只返回公开的配置信息，不包含敏感数据
        public_config = {
            'saml': {
                'entity_id': config.get('saml', {}).get('entity_id'),
                'acs_url': config.get('saml', {}).get('acs_url'),
                'idp_entity_id': config.get('saml', {}).get('idp_entity_id')
            },
            'oidc': {
                'issuer': config.get('oidc', {}).get('issuer'),
                'access_token_exp': config.get('oidc', {}).get('access_token_exp'),
                'id_token_exp': config.get('oidc', {}).get('id_token_exp')
            },
            'database': {
                'type': 'postgresql' if 'postgresql' in config.get('database', {}).get('url', '') else 'sqlite'
            }
        }
        
        return jsonify(public_config)
    except Exception as e:
        return handle_api_error('system_error', f'获取配置信息失败: {str(e)}', 500)

@api_bp.errorhandler(404)
def not_found(error):
    """
    404 错误处理
    """
    return jsonify(error_schema.dump({
        'error': 'not_found',
        'message': '请求的资源不存在'
    })), 404

@api_bp.errorhandler(405)
def method_not_allowed(error):
    """
    405 错误处理
    """
    return jsonify(error_schema.dump({
        'error': 'method_not_allowed',
        'message': '不支持的请求方法'
    })), 405

@api_bp.errorhandler(500)
def internal_error(error):
    """
    500 错误处理
    """
    return jsonify(error_schema.dump({
        'error': 'internal_server_error',
        'message': '服务器内部错误'
    })), 500
