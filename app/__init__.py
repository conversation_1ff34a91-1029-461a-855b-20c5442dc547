from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
import os
import sys

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 初始化扩展
db = SQLAlchemy()
migrate = Migrate()

def create_app(config_name='default'):
    app = Flask(__name__)
    
    # 加载配置
    if config_name == 'default':
        # 尝试加载 YAML 配置文件
        try:
            from config import load_config
            config = load_config()
            app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dev-secret-key')
            app.config['SQLALCHEMY_DATABASE_URI'] = config.get('database', {}).get('url', 'sqlite:///app.db')
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            app.config['SECRET_KEY'] = 'dev-secret-key'
            app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///app.db'
            app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    
    # 初始化扩展
    db.init_app(app)
    migrate.init_app(app, db)
    
    # 注册蓝图
    from .saml import saml_bp
    from .oidc import oidc_bp
    from .auth import auth_bp
    
    app.register_blueprint(saml_bp, url_prefix='/saml')
    app.register_blueprint(oidc_bp, url_prefix='/oauth')
    app.register_blueprint(auth_bp)
    
    # 初始化 OIDC 授权服务器
    from .oidc import init_app
    init_app(app)
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
    
    @app.route('/')
    def index():
        return 'SAML2 SP + OIDC IdP Server'
    
    return app