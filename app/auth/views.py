"""
认证管理视图处理模块
"""
from flask import request, render_template, redirect, url_for, session
from . import auth_bp
from app.models.user import User

@auth_bp.route('/logout')
def logout():
    """
    用户登出
    """
    # 清除会话
    session.clear()
    return redirect(url_for('index'))

@auth_bp.route('/profile')
def profile():
    """
    用户个人信息页面
    """
    # 检查用户是否已登录
    if 'user_id' not in session:
        return redirect(url_for('saml.login'))
    
    user = User.query.get(session['user_id'])
    if not user:
        session.clear()
        return redirect(url_for('saml.login'))
    
    return f"用户信息: {user.username}, 邮箱: {user.email}"