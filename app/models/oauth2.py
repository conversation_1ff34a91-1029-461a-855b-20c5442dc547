from app import db
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.mutable import MutableDict
from datetime import datetime
import secrets

# 为兼容不同数据库，如果没有 PostgreSQL，则使用 JSON 类型
try:
    JSONType = JSONB
except:
    from sqlalchemy import JSON
    JSONType = JSON

class OAuth2Client(db.Model):
    __tablename__ = 'oauth2_clients'
    
    id = Column(Integer, primary_key=True)
    client_id = Column(String(48), unique=True, nullable=False)
    client_secret = Column(String(120), nullable=False)
    client_name = Column(String(120))
    client_uri = Column(Text)
    logo_uri = Column(Text)
    contact = Column(Text)
    tos_uri = Column(Text)
    policy_uri = Column(Text)
    redirect_uris = Column(MutableDict.as_mutable(JSONType), nullable=False)
    grant_types = Column(MutableDict.as_mutable(JSONType))
    response_types = Column(MutableDict.as_mutable(JSONType))
    token_endpoint_auth_method = Column(String(48), default='client_secret_basic')
    scope = Column(Text, default='openid')
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<OAuth2Client {self.client_name}>'
    
    def check_client_secret(self, client_secret):
        return secrets.compare_digest(self.client_secret, client_secret)
    
    def check_redirect_uri(self, redirect_uri):
        return redirect_uri in self.redirect_uris
    
    def check_grant_type(self, grant_type):
        if self.grant_types is None:
            return True
        return grant_type in self.grant_types
    
    def check_response_type(self, response_type):
        if self.response_types is None:
            return True
        return response_type in self.response_types


class OAuth2AuthorizationCode(db.Model):
    __tablename__ = 'oauth2_authorization_codes'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(120), unique=True, nullable=False)
    client_id = Column(String(48), ForeignKey('oauth2_clients.client_id'), nullable=False)
    redirect_uri = Column(Text)
    response_type = Column(Text)
    scope = Column(Text)
    auth_time = Column(Integer, nullable=False)
    code_challenge = Column(Text)
    code_challenge_method = Column(String(48))
    nonce = Column(Text)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    def __repr__(self):
        return f'<OAuth2AuthorizationCode {self.code[:10]}...>'
    
    def is_expired(self):
        # 授权码有效期为10分钟
        return (datetime.utcnow() - self.created_at).total_seconds() > 600


class OAuth2Token(db.Model):
    __tablename__ = 'oauth2_tokens'
    
    id = Column(Integer, primary_key=True)
    client_id = Column(String(48), ForeignKey('oauth2_clients.client_id'), nullable=False)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    token_type = Column(String(40))
    access_token = Column(String(255), unique=True, nullable=False)
    refresh_token = Column(String(255), unique=True)
    scope = Column(Text)
    revoked = Column(Integer, default=0)
    issued_at = Column(Integer, nullable=False)
    expires_in = Column(Integer, nullable=False)
    nonce = Column(Text)
    
    def __repr__(self):
        return f'<OAuth2Token {self.access_token[:10]}...>'
    
    def check_token(self, token):
        return secrets.compare_digest(self.access_token, token)
    
    def is_refresh_token_active(self):
        if self.revoked:
            return False
        return True
    
    def is_access_token_active(self):
        if self.revoked:
            return False
        # 检查是否过期
        current_time = datetime.utcnow()
        issued_time = datetime.fromtimestamp(self.issued_at)
        expiration_time = issued_time.timestamp() + self.expires_in
        return datetime.utcnow().timestamp() < expiration_time