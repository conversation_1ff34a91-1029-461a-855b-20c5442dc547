from app import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.ext.mutable import MutableDict

# 为兼容不同数据库，如果没有 PostgreSQL，则使用 JSON 类型
try:
    JSONType = JSONB
except:
    from sqlalchemy import JSON
    JSONType = JSON

class User(db.Model):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False)
    email = Column(String(120), unique=True, nullable=True)
    # SAML 相关属性
    saml_subject_id = Column(String(255), unique=True, nullable=True)
    saml_attributes = Column(MutableDict.as_mutable(JSONType), nullable=True)
    # 其他用户信息
    created_at = Column(DateTime, default=datetime.utcnow)
    last_login = Column(DateTime)
    
    def __repr__(self):
        return f'<User {self.username}>'
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'last_login': self.last_login.isoformat() if self.last_login else None
        }