"""
OIDC 服务器配置模块
"""
from authlib.integrations.flask_oauth2 import AuthorizationServer
from authlib.integrations.sqla_oauth2 import (
    create_query_client_func,
    create_save_token_func,
    create_revocation_endpoint,
)
from authlib.oauth2.rfc6749 import grants
from authlib.oauth2.rfc7636 import CodeChallenge
from authlib.oidc.core import UserInfo
from authlib.oidc.core.grants import (
    OpenIDToken,
    OpenIDCode,
    OpenIDImplicitGrant as _OpenIDImplicitGrant,
)
from app import db
from app.models.oauth2 import OAuth2Client, OAuth2AuthorizationCode, OAuth2Token
from app.models.user import User
import time

# 查询客户端函数
query_client = create_query_client_func(db.session, OAuth2Client)

# 保存令牌函数
save_token = create_save_token_func(db.session, OAuth2Token)

# 创建授权服务器
authorization = AuthorizationServer(
    query_client=query_client,
    save_token=save_token,
)

def exists_nonce(nonce, request):
    """
    检查 nonce 是否存在
    """
    exists = OAuth2AuthorizationCode.query.filter_by(
        client_id=request.client_id, nonce=nonce
    ).first()
    return bool(exists)

class OpenIDCodeGrant(OpenIDCode):
    """
    OpenID Code 授权类型
    """
    def exists_nonce(self, nonce, request):
        return exists_nonce(nonce, request)

    def get_jwt_config(self, request):
        # 从配置中获取 JWT 配置
        # 这里应该从实际配置中加载
        return {
            'key': 'certs/private.key',
            'alg': 'RS256',
            'iss': 'https://sp.example.com',
            'exp': 3600,
        }

    def generate_user_info(self, user, scope):
        user_info = UserInfo(sub=str(user.id), name=user.username)
        if 'email' in scope:
            user_info['email'] = user.email
        return user_info

class AuthorizationCodeGrant(grants.AuthorizationCodeGrant):
    """
    授权码授权类型
    """
    TOKEN_ENDPOINT_AUTH_METHODS = [
        'client_secret_basic',
        'client_secret_post',
        'none',
    ]

    def save_authorization_code(self, code, request):
        code_challenge = request.data.get('code_challenge')
        code_challenge_method = request.data.get('code_challenge_method')
        nonce = request.data.get('nonce')
        
        auth_code = OAuth2AuthorizationCode(
            code=code,
            client_id=request.client.client_id,
            redirect_uri=request.redirect_uri,
            response_type=request.response_type,
            scope=request.scope,
            auth_time=int(time.time()),
            code_challenge=code_challenge,
            code_challenge_method=code_challenge_method,
            nonce=nonce,
            user_id=request.user.id,
        )
        db.session.add(auth_code)
        db.session.commit()
        return auth_code

    def query_authorization_code(self, code, client):
        auth_code = OAuth2AuthorizationCode.query.filter_by(
            code=code, client_id=client.client_id
        ).first()
        if auth_code and not auth_code.is_expired():
            return auth_code

    def delete_authorization_code(self, authorization_code):
        db.session.delete(authorization_code)
        db.session.commit()

    def authenticate_user(self, authorization_code):
        user = User.query.get(authorization_code.user_id)
        return user

class PasswordGrant(grants.ResourceOwnerPasswordCredentialsGrant):
    """
    密码授权类型
    """
    def authenticate_user(self, username, password):
        # 在本系统中不支持密码授权类型，因为用户认证通过SAML完成
        return None

class RefreshTokenGrant(grants.RefreshTokenGrant):
    """
    刷新令牌授权类型
    """
    def authenticate_refresh_token(self, refresh_token):
        token = OAuth2Token.query.filter_by(refresh_token=refresh_token).first()
        if token and token.is_refresh_token_active():
            return token

    def authenticate_user(self, credential):
        user = User.query.get(credential.user_id)
        return user

    def revoke_old_credential(self, credential):
        credential.revoked = 1
        db.session.add(credential)
        db.session.commit()

# 注册授权类型
authorization.register_grant(
    AuthorizationCodeGrant, 
    [OpenIDCodeGrant, CodeChallenge]
)

authorization.register_grant(PasswordGrant)
authorization.register_grant(RefreshTokenGrant)

# 注册令牌撤销端点
revocation_cls = create_revocation_endpoint(db.session, OAuth2Token)
authorization.register_endpoint(revocation_cls)