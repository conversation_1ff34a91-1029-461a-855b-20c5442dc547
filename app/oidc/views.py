"""
OIDC IdP 视图处理模块
"""
from flask import request, render_template, redirect, url_for, session, jsonify
from . import oidc_bp
from .server import authorization
from app import db
from app.models.user import User
from app.models.oauth2 import OAuth2Client, OAuth2Token
from authlib.oidc.core import CodeIDToken
from authlib.common.security import generate_token
import time
import json

def current_user():
    """
    获取当前用户
    """
    if 'user_id' in session:
        user = User.query.get(session['user_id'])
        return user
    return None

@oidc_bp.route('/authorize', methods=['GET', 'POST'])
def authorize():
    """
    OIDC 授权端点
    """
    # 检查用户是否已登录
    user = current_user()
    if not user:
        # 重定向到 SAML 登录
        return redirect(url_for('saml.login', next=request.url))
    
    # 验证请求参数
    try:
        grant = authorization.validate_consent_request(end_user=user)
    except Exception as error:
        return str(error), 400
    
    # 如果是 GET 请求，显示同意页面
    if request.method == 'GET':
        # 简化处理，自动同意所有请求
        return redirect(url_for('.authorize', **request.args, _external=True, _scheme='https'))
    
    # 处理同意授权
    if request.form.get('confirm') == 'yes':
        # 用户同意授权
        scope = request.form.get('scope', grant.request.scope)
        token = authorization.create_authorization_response(grant_user=user, scope=scope)
        return token
    
    # 用户拒绝授权
    return authorization.create_authorization_response(grant_user=None)

@oidc_bp.route('/token', methods=['POST'])
def issue_token():
    """
    OIDC 令牌端点
    """
    return authorization.create_token_response()

@oidc_bp.route('/userinfo')
def userinfo():
    """
    OIDC 用户信息端点
    """
    user = current_user()
    if not user:
        return jsonify(error='未认证'), 401
    
    # 获取访问令牌
    token = request.headers.get('Authorization')
    if not token:
        return jsonify(error='缺少访问令牌'), 401
    
    # 验证令牌
    # 这里简化处理，实际应该验证令牌有效性
    return jsonify(
        sub=str(user.id),
        name=user.username,
        email=user.email
    )

@oidc_bp.route('/jwks')
def jwks():
    """
    JWKS 端点 - 提供公钥信息
    """
    # 这里应该返回实际的 JWKS 信息
    # 由于我们还没有生成实际的密钥对，返回示例数据
    return jsonify({
        "keys": [
            {
                "kty": "RSA",
                "use": "sig",
                "kid": "example-key-id",
                "n": "example-modulus",
                "e": "AQAB"
            }
        ]
    })

@oidc_bp.route('/.well-known/openid-configuration')
def openid_configuration():
    """
    OpenID 配置发现端点
    """
    issuer = request.url_root.rstrip('/')  # 实际应该从配置中读取
    
    config = {
        "issuer": issuer,
        "authorization_endpoint": url_for('.authorize', _external=True, _scheme='https'),
        "token_endpoint": url_for('.issue_token', _external=True, _scheme='https'),
        "userinfo_endpoint": url_for('.userinfo', _external=True, _scheme='https'),
        "jwks_uri": url_for('.jwks', _external=True, _scheme='https'),
        "response_types_supported": [
            "code",
            "token",
            "id_token",
            "code token",
            "code id_token",
            "token id_token",
            "code token id_token"
        ],
        "subject_types_supported": ["public"],
        "id_token_signing_alg_values_supported": ["RS256"],
        "scopes_supported": ["openid", "email", "profile"],
        "token_endpoint_auth_methods_supported": [
            "client_secret_basic",
            "client_secret_post"
        ],
        "claims_supported": [
            "aud",
            "email",
            "exp",
            "family_name",
            "given_name",
            "iat",
            "iss",
            "locale",
            "name",
            "picture",
            "sub"
        ]
    }
    
    return jsonify(config)