"""
SAML SP 配置管理模块
"""
import os
from config import load_config

def get_saml_config():
    """
    获取 SAML 配置
    """
    try:
        config = load_config()
        saml_config = config.get('saml', {})
        
        # 构建 PySAML2 配置字典
        saml_settings = {
            'entityid': saml_config.get('entity_id'),
            'service': {
                'sp': {
                    'name': 'SAML2 SP',
                    'name_id_format': 'urn:oasis:names:tc:SAML:2.0:nameid-format:transient',
                    'endpoints': {
                        'assertion_consumer_service': [
                            (saml_config.get('acs_url'), 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST'),
                        ],
                    },
                    'required_attributes': ['uid', 'email', 'displayName'],
                    'optional_attributes': ['eduPersonAffiliation'],
                    'idp': {
                        saml_config.get('idp_entity_id'): {
                            'single_sign_on_service': {
                                'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect': 
                                    'https://idp.example.com/sso/redirect',
                                'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST': 
                                    'https://idp.example.com/sso/post',
                            },
                        },
                    },
                },
            },
            'metadata': {
                'local': [saml_config.get('idp_metadata_url')],
            },
        }
        
        # 处理证书配置
        sp_cert = saml_config.get('sp_cert')
        sp_key = saml_config.get('sp_key')
        
        if sp_cert and os.path.exists(sp_cert):
            saml_settings['service']['sp']['cert_file'] = sp_cert
        
        if sp_key and os.path.exists(sp_key):
            saml_settings['service']['sp']['key_file'] = sp_key
            
        return saml_settings
    except Exception as e:
        print(f"加载 SAML 配置失败: {e}")
        # 返回默认配置
        return {
            'entityid': 'urn:example:sp',
            'service': {
                'sp': {
                    'name': 'SAML2 SP',
                    'name_id_format': 'urn:oasis:names:tc:SAML:2.0:nameid-format:transient',
                    'endpoints': {
                        'assertion_consumer_service': [
                            ('http://localhost:5000/saml/acs', 'urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST'),
                        ],
                    },
                },
            },
        }