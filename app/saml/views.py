"""
SAML SP 视图处理模块
"""
from flask import request, render_template, redirect, url_for, session, current_app
from . import saml_bp
from .config import get_saml_config
from saml2 import BINDING_HTTP_REDIRECT, BINDING_HTTP_POST
from saml2.client import Saml2Client
from saml2.config import Config as Saml2Config
import uuid
from app.models.user import User
from app import db

def get_saml_client():
    """
    获取 SAML 客户端实例
    """
    saml_settings = get_saml_config()
    sp_config = Saml2Config()
    sp_config.load(saml_settings)
    sp_config.allow_unknown_attributes = True
    saml_client = Saml2Client(config=sp_config)
    return saml_client

@saml_bp.route('/login')
def login():
    """
    发起 SAML 认证请求
    """
    saml_client = get_saml_client()
    
    # 生成请求ID
    reqid, info = saml_client.prepare_for_authenticate(
        relay_state=request.args.get('next', '/')
    )
    
    # 保存请求ID到会话
    session['saml_request_id'] = reqid
    
    # 重定向到 IdP
    redirect_url = None
    for key, value in info['headers']:
        if key == 'Location':
            redirect_url = value
            break
    
    return redirect(redirect_url)

@saml_bp.route('/acs', methods=['POST'])
def assertion_consumer_service():
    """
    处理 SAML 响应 (Assertion Consumer Service)
    """
    saml_client = get_saml_client()
    
    # 从POST数据中获取SAML响应
    saml_response = request.form['SAMLResponse']
    
    # 获取请求ID
    req_id = session.get('saml_request_id')
    
    # 解析SAML响应
    authn_response = saml_client.parse_authn_request_response(
        saml_response,
        BINDING_HTTP_POST,
        {req_id: '/'}
    )
    
    if authn_response is None:
        return "无效的 SAML 响应", 400
    
    # 获取用户属性
    user_info = authn_response.get_identity()
    subject_id = authn_response.get_subject().text
    
    # 查找或创建用户
    user = User.query.filter_by(saml_subject_id=subject_id).first()
    if not user:
        # 创建新用户
        username = user_info.get('uid', [str(uuid.uuid4())[:8]])[0]
        email = user_info.get('email', [None])[0]
        
        user = User(
            username=username,
            email=email,
            saml_subject_id=subject_id,
            saml_attributes=user_info
        )
        db.session.add(user)
        db.session.commit()
    else:
        # 更新用户属性
        user.saml_attributes = user_info
        user.last_login = db.func.current_timestamp()
        db.session.commit()
    
    # 设置用户会话
    session['user_id'] = user.id
    session['logged_in'] = True
    
    # 清除SAML请求ID
    session.pop('saml_request_id', None)
    
    # 重定向到原始请求页面
    relay_state = request.form.get('RelayState', '/')
    return redirect(relay_state)

@saml_bp.route('/metadata')
def metadata():
    """
    提供 SP 元数据
    """
    saml_client = get_saml_client()
    metadata_str = saml_client.metadata()
    return metadata_str, 200, {'Content-Type': 'text/xml'}