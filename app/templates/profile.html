{% extends "base.html" %}

{% block title %}用户信息{% endblock %}

{% block content %}
<h2>用户信息</h2>

<div>
    <p><strong>用户名:</strong> {{ user.username }}</p>
    <p><strong>邮箱:</strong> {{ user.email or '未设置' }}</p>
    <p><strong>注册时间:</strong> {{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else '未知' }}</p>
    <p><strong>最后登录:</strong> {{ user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '首次登录' }}</p>
</div>

<h3>SAML 属性信息</h3>
<div>
    {% if user.saml_attributes %}
        <ul>
        {% for key, value in user.saml_attributes.items() %}
            <li><strong>{{ key }}:</strong> {{ value }}</li>
        {% endfor %}
        </ul>
    {% else %}
        <p>暂无 SAML 属性信息</p>
    {% endif %}
</div>

<p><a href="{{ url_for('auth.logout') }}" class="btn">退出登录</a></p>
{% endblock %}