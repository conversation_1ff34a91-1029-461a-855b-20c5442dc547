saml:
  entity_id: "urn:example:sp"
  acs_url: "https://sp.example.com/saml/acs"
  sp_cert: "certs/sp.crt"
  sp_key: "certs/sp.key"
  idp_metadata_url: "https://idp.example.com/metadata.xml"
  idp_entity_id: "https://idp.example.com/metadata"

oidc:
  issuer: "https://sp.example.com"
  jwks:
    private_key: "certs/private.key"
    public_key: "certs/public.pem"
  access_token_exp: 3600
  id_token_exp: 3600

database:
  url: "sqlite:///app.db"

redis:
  host: "localhost"
  port: 6379
  db: 0