[tool.poetry]
name = "oidc-saml-system"
version = "1.0.0"
description = "基于 Flask 的 SAML2 SP + OIDC IdP 认证中间件系统"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}, {include = "api"}, {include = "config"}]

[tool.poetry.dependencies]
python = "^3.9"
Flask = "^2.0.0"
Flask-SQLAlchemy = "^2.5.1"
Flask-Migrate = "^3.1.0"
pysaml2 = "^7.0.0"
Authlib = "^1.6.0"
PyYAML = "^5.4.1"
redis = "^4.5.3"
psycopg2-binary = "^2.9.5"
Flask-CORS = "^4.0.0"
marshmallow = "^3.19.0"
cryptography = "^3.4.8"
lxml = "^4.6.0"
xmlsec = "^1.3.13"

[tool.poetry.group.dev.dependencies]
pytest = "^7.0.0"
pytest-flask = "^1.2.0"
pytest-cov = "^4.0.0"
black = "^23.0.0"
flake8 = "^6.0.0"
mypy = "^1.0.0"
pre-commit = "^3.0.0"

[tool.poetry.group.test.dependencies]
factory-boy = "^3.2.0"
faker = "^18.0.0"
responses = "^0.23.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    ".eggs",
    "*.egg"
]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pysaml2.*",
    "saml2.*",
    "xmlsec.*",
    "lxml.*"
]
ignore_missing_imports = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=app",
    "--cov=api",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml"
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests"
]

[tool.coverage.run]
source = ["app", "api"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:"
]
