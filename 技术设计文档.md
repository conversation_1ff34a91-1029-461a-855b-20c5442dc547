# 技术设计文档：基于 Flask 的 SAML2 SP + OIDC IdP 系统

## 1. 概述

本文档详细描述了基于 Flask 框架实现 SAML2 服务提供者 (SP) 和 OpenID Connect 身份提供者 (IdP) 的技术设计方案。系统将作为认证中间件，实现与外部 SAML IdP 的交互，并向内部应用提供 OIDC 认证服务。

## 2. 技术架构

### 2.1 技术选型

| 组件 | 技术/工具 | 说明 |
|------|----------|------|
| 核心框架 | Flask 2.0+ | 轻量级 Web 框架，易于扩展 |
| SAML2 实现 | PySAML2 7.0+ | Python 实现的 SAML2 库 |
| OIDC 实现 | Authlib 1.6+ | 构建 OAuth/OIDC 服务器的库 |
| 数据库 | SQLAlchemy 1.4+ | ORM 工具 |
| 缓存/会话 | Redis 4.5+ | 会话和临时数据存储 |
| 配置管理 | PyYAML 5.4+ | YAML 配置文件解析 |

### 2.2 系统架构图

```
+----------------------+ SAML2 +----------------------------------+ 
| 外部 SAML2 身份提供者 |<------->| 我们的系统 (SAML2 SP + OIDC IdP) | 
+----------------------+       +----------------------------------+ 
                                        |
                                        | OIDC (授权码流程)
                                        v
                                +----------------+
                                | OIDC 客户端应用 |
                                +----------------+
```

## 3. 详细设计

### 3.1 目录结构

```
oidc/
├── app/                        # Flask 应用主目录
│   ├── __init__.py            # 应用初始化
│   ├── models/                # 数据模型
│   │   ├── __init__.py
│   │   ├── user.py            # 用户模型
│   │   └── oauth2.py          # OAuth2 相关模型
│   ├── saml/                  # SAML SP 模块
│   │   ├── __init__.py
│   │   ├── config.py          # SAML 配置管理
│   │   ├── views.py           # SAML 视图处理
│   │   └── handlers.py        # SAML 请求/响应处理
│   ├── oidc/                  # OIDC IdP 模块
│   │   ├── __init__.py
│   │   ├── server.py          # OIDC 服务器配置
│   │   ├── views.py           # OIDC 视图处理
│   │   └── grants.py          # 自定义授权类型
│   ├── auth/                  # 认证管理模块
│   │   ├── __init__.py
│   │   ├── sessions.py        # 会话管理
│   │   └── decorators.py      # 认证装饰器
│   └── templates/             # HTML 模板
│       ├── base.html
│       ├── login.html
│       └── consent.html
├── config/                    # 配置文件
│   ├── __init__.py
│   ├── default.py             # 默认配置
│   └── config.yaml            # YAML 配置文件
├── migrations/                # 数据库迁移脚本
├── tests/                     # 测试代码
├── certs/                     # 证书文件
├── static/                    # 静态资源
├── requirements.txt           # 依赖包列表
├── run.py                     # 应用入口
└── README.md                  # 项目说明文档
```

### 3.2 核心模块设计

#### 3.2.1 Flask 应用初始化

应用将采用工厂模式创建，便于测试和配置管理：

```python
# app/__init__.py
from flask import Flask
from flask_sqlalchemy import SQLAlchemy

db = SQLAlchemy()

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化扩展
    db.init_app(app)
    
    # 注册蓝图
    from .saml import saml_bp
    from .oidc import oidc_bp
    from .auth import auth_bp
    
    app.register_blueprint(saml_bp, url_prefix='/saml')
    app.register_blueprint(oidc_bp, url_prefix='/oauth')
    app.register_blueprint(auth_bp)
    
    return app
```

#### 3.2.2 SAML SP 模块

SAML 模块负责与外部 IdP 交互：

1. **配置管理** (`app/saml/config.py`)
   - 加载和管理 PySAML2 配置
   - 处理 SP 元数据

2. **视图处理** (`app/saml/views.py`)
   - `/saml/login` - 发起 SAML 认证请求
   - `/saml/acs` - 处理 SAML 响应
   - `/saml/metadata` - 提供 SP 元数据

#### 3.2.3 OIDC IdP 模块

OIDC 模块实现 OAuth2 授权服务器：

1. **服务器配置** (`app/oidc/server.py`)
   - 配置 Authlib OAuth2Server
   - 注册令牌端点和授权端点

2. **视图处理** (`app/oidc/views.py`)
   - `/oauth/authorize` - 授权端点
   - `/oauth/token` - 令牌端点
   - `/oauth/userinfo` - 用户信息端点
   - `/.well-known/openid-configuration` - 配置发现端点

3. **授权类型** (`app/oidc/grants.py`)
   - 实现授权码流程
   - 处理用户同意授权

#### 3.2.4 用户和认证模块

1. **用户模型** (`app/models/user.py`)
   - 用户基本信息
   - SAML 属性映射
   - 本地认证信息

2. **会话管理** (`app/auth/sessions.py`)
   - 用户登录状态管理
   - 会话超时处理

3. **认证装饰器** (`app/auth/decorators.py`)
   - 登录状态检查
   - 权限验证

### 3.3 数据模型设计

#### 3.3.1 用户模型

```python
# app/models/user.py
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=True)
    # SAML 相关属性
    saml_subject_id = db.Column(db.String(255), unique=True, nullable=True)
    saml_attributes = db.Column(db.JSON, nullable=True)
    # 其他用户信息
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
```

#### 3.3.2 OAuth2 模型

```python
# app/models/oauth2.py
class OAuth2Client(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.String(48), unique=True, nullable=False)
    client_secret = db.Column(db.String(120), nullable=False)
    client_name = db.Column(db.String(120))
    client_uri = db.Column(db.Text)
    logo_uri = db.Column(db.Text)
    contact = db.Column(db.Text)
    tos_uri = db.Column(db.Text)
    policy_uri = db.Column(db.Text)
    redirect_uris = db.Column(db.JSON, nullable=False)
    grant_types = db.Column(db.JSON)
    response_types = db.Column(db.JSON)
    token_endpoint_auth_method = db.Column(db.String(48), default='client_secret_basic')
    scope = db.Column(db.Text, default='openid')

class OAuth2AuthorizationCode(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(120), unique=True, nullable=False)
    client_id = db.Column(db.String(48), nullable=False)
    redirect_uri = db.Column(db.Text)
    response_type = db.Column(db.Text)
    scope = db.Column(db.Text)
    auth_time = db.Column(db.Integer, nullable=False)
    code_challenge = db.Column(db.Text)
    code_challenge_method = db.Column(db.String(48))
    nonce = db.Column(db.Text)
    user_id = db.Column(db.Integer, nullable=False)
```

### 3.4 安全设计

1. **会话安全**
   - 使用安全的 Cookie 设置 (HttpOnly, Secure)
   - 实现 CSRF 保护
   - 会话超时机制

2. **SAML 安全**
   - 响应签名验证
   - 重放攻击防护
   - HTTPS 通信

3. **OIDC 安全**
   - JWT 签名验证 (RS256)
   - state 参数防止 CSRF
   - nonce 参数防止重放攻击

### 3.5 配置管理

使用 YAML 配置文件管理不同环境的配置：

```yaml
# config/config.yaml
saml:
  entity_id: "urn:example:sp"
  acs_url: "https://sp.example.com/saml/acs"
  sp_cert: "certs/sp.crt"
  sp_key: "certs/sp.key"
  idp_metadata_url: "https://idp.example.com/metadata.xml"
  idp_entity_id: "https://idp.example.com/metadata"

oidc:
  issuer: "https://sp.example.com"
  jwks:
    private_key: "certs/private.key"
    public_key: "certs/public.pem"
  access_token_exp: 3600
  id_token_exp: 3600

database:
  url: "sqlite:///data.db"

redis:
  host: "localhost"
  port: 6379
  db: 0
```

### 3.6 API 端点设计

#### 3.6.1 SAML 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/saml/login` | GET | 发起 SAML 认证请求 |
| `/saml/acs` | POST | 处理 SAML 响应 |
| `/saml/metadata` | GET | 提供 SP 元数据 |

#### 3.6.2 OIDC 端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/oauth/authorize` | GET/POST | 授权端点 |
| `/oauth/token` | POST | 令牌端点 |
| `/oauth/userinfo` | GET/POST | 用户信息端点 |
| `/.well-known/openid-configuration` | GET | 配置发现端点 |

## 4. 部署架构

### 4.1 开发环境
- 使用 Flask 内置服务器
- SQLite 作为数据库
- 本地 Redis 实例

### 4.2 生产环境
- 使用 Gunicorn 或 uWSGI 作为 WSGI 服务器
- PostgreSQL 或 MySQL 作为数据库
- Redis 集群用于会话存储
- Nginx 作为反向代理

## 5. 测试策略

### 5.1 单元测试
- 对每个模块进行独立测试
- 使用 pytest 作为测试框架

### 5.2 集成测试
- 测试 SAML 和 OIDC 的完整流程
- 模拟外部 IdP 行为

### 5.3 安全测试
- 验证各种安全机制的有效性
- 测试常见的攻击向量

## 6. 监控和日志

### 6.1 日志记录
- 记录关键操作和错误信息
- 使用结构化日志便于分析

### 6.2 健康检查
- 提供 `/health` 端点检查系统状态
- 监控数据库和 Redis 连接状态