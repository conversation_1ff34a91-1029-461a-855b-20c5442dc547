# SAML2 服务提供者 (SP) + OIDC 身份提供者 (IdP) 项目

## 项目简介
本项目旨在实现一个同时扮演 SAML2 服务提供者 (SP) 和 OpenID Connect 身份提供者 (IdP) 的认证中间件。SAML2 是一种基于 XML 的开放标准，用于在身份提供者和服务提供者之间传递认证断言，实现单点登录（SSO）:contentReference[oaicite:0]{index=0}。OpenID Connect (OIDC) 则是在 OAuth2.0 协议基础上加入身份验证层的协议，用于向客户端提供身份令牌和用户信息:contentReference[oaicite:1]{index=1}。我们使用 PySAML2 库来实现 SAML2 SP 功能，PySAML2 是一个纯 Python 实现的 SAML2 库，支持构建完整的 SP 或 IdP:contentReference[oaicite:2]{index=2}；同时使用 Authlib 框架搭建 OIDC 服务端，Authlib 是一个构建 OAuth/OIDC 服务器的顶级 Python 库:contentReference[oaicite:3]{index=3}。通过整合这两种协议，本系统可以从外部 SAML IdP 接收认证断言，并向内部应用发放 OIDC 访问令牌，提供跨协议的统一认证服务。

## 系统架构图（文本）
下图展示了系统的基本架构：系统一方面作为 SAML2 SP 与外部 IdP 交互，另一方面作为 OIDC IdP 向客户端应用提供令牌。用户在浏览器上首先访问 SP 端点，系统发起 SAML2 认证请求到外部 SAML IdP，完成认证后 SAML 断言返回到本系统。随后，本系统可以根据该用户信息生成 OIDC 的授权码、ID Token 等，供客户端应用使用。  

+----------------------+ SAML2 +----------------------------------+
| 外部 SAML2 身份提供者 |<------->| 我们的系统 (SAML2 SP + OIDC IdP) |
+----------------------+ +----------------------------------+
|
| OIDC (授权码流程)
v
+----------------+
| OIDC 客户端 |
+----------------+
bash
复制
编辑

## 技术选型
| 技术/组件        | 作用              | 说明                                    |
|----------------|-----------------|---------------------------------------|
| **Python 3.9+**  | 编程语言          | 生态丰富，支持 PySAML2 和 Authlib 等库                |
| **Flask/Django/FastAPI** | Web 框架       | 处理 HTTP 请求，提供 RESTful 接口和页面渲染            |
| **PySAML2**     | SAML2 SP 实现     | 纯 Python 的 SAML2 实现库，支持构建 SP/IdP:contentReference[oaicite:4]{index=4}  |
| **Authlib**     | OIDC IdP 实现     | 构建 OAuth2/OpenID Connect 服务器的库:contentReference[oaicite:5]{index=5}       |
| **SQLAlchemy**  | ORM/数据库管理    | 用户数据和客户端凭证等持久化存储                         |
| **Redis**       | 缓存/会话管理     | 存储会话、Nonce、临时令牌等，提高性能与安全性               |

## 功能模块说明
### SAML2 服务提供者 (SP)
- 配置和管理 SAML2 SP 参数：使用 PySAML2 加载 SP 配置，包括实体 ID、Assertion Consumer Service (ACS) URL、证书等。  
- 处理认证流程：实现 SAML2 SP 发起认证请求并接收断言的流程。用户访问 `/saml/login` 等端点时，生成 AuthnRequest 发送到外部 IdP。IdP 返回包含用户身份信息的 SAML Response 到本地 ACS（如 `/saml/acs`），系统验证签名并提取用户属性。  
- 元数据管理：生成和提供 SP 的元数据文件（metadata），以便 IdP 识别并信任本系统。  

### OIDC 身份提供者 (IdP)
- OAuth2/OIDC 服务器：使用 Authlib 实现标准的 OAuth2 授权码流程。提供 `/oauth/authorize`, `/oauth/token`, `/oauth/userinfo` 等端点。支持向可信客户端颁发 `authorization code`、`access_token` 和 `id_token`。  
- 客户端管理：维护注册的 OIDC 客户端（包括 client_id、secret、回调地址等），并对授权请求进行校验。  
- 登录与授权：当客户端访问授权端点时，系统检查用户登录状态（可复用 SAML 登录会话），如果需要则引导用户进行登录（例如通过 SAML SP 登录或本地登录）。然后根据用户同意情况颁发授权码并最终颁发 JWT 格式的 ID Token。  

### 用户管理
- 用户数据模型：使用数据库存储用户信息，包括 SAML 返回的属性、用户ID、角色等。  
- 账户创建与映射：首次从 SAML 认证成功后，可自动创建本地用户账户并关联 SAML 属性。实现用户登录后信息的持久化和后续识别。  
- 会话管理：在用户认证通过后，创建登录会话（如使用 Flask-Login 或自定义会话机制），管理用户的登录状态和超时。  

## 目录结构 (示例)
```text
my_project/
├── saml_sp/                   # SAML 服务提供者模块
│   ├── sp_config.py          # PySAML2 配置
│   ├── views.py              # 处理 /saml/login, /saml/acs 等视图
│   └── ...
├── oidc_idp/                  # OIDC 身份提供者模块
│   ├── oidc.py               # Authlib 授权服务器配置与端点实现
│   ├── models.py             # OAuth 客户端、授权码等模型
│   └── ...
├── user_management/           # 用户与会话管理
│   ├── models.py             # 用户数据模型
│   ├── auth_utils.py         # 登录校验、Session 管理工具
│   └── ...
├── templates/                 # 可选的登录/同意页面模板
│   ├── login.html
│   ├── consent.html
│   └── ...
├── static/                    # 静态资源（CSS/JS 等）
├── config.yaml                # 项目配置文件示例
├── requirements.txt           # 依赖列表
└── README.md                  # 项目说明文档
项目初始化与依赖安装说明
克隆项目并进入目录：git clone <repo_url>，然后 cd my_project。
创建虚拟环境：python3 -m venv venv，激活环境（Linux/Mac: source venv/bin/activate，Windows: venv\Scripts\activate）。
安装依赖：pip install --upgrade pip，然后执行 pip install -r requirements.txt。
示例 requirements.txt 内容（具体版本可根据需要调整）：
text
复制
编辑
Flask==2.0.0
pysaml2==7.0.0
Authlib==1.6.0
SQLAlchemy==1.4.0
PyYAML==5.4.1
redis==4.5.3
配置文件模板
以下给出一个 YAML 配置模板示例，集中配置 SAML 和 OIDC 相关参数（实际使用时可通过 config.yaml 加载）：
yaml
复制
编辑
saml:
  entity_id: "urn:example:sp"             # 本 SP 实体 ID
  acs_url: "https://sp.example.com/saml/acs"  # Assertion Consumer Service URL
  sp_cert: "certs/sp.crt"                # SP 公钥证书路径
  sp_key: "certs/sp.key"                 # SP 私钥路径
  idp_metadata_url: "https://idp.example.com/metadata.xml"  # IdP 元数据地址
  idp_entity_id: "https://idp.example.com/metadata"        # IdP 实体 ID

oidc:
  issuer: "https://sp.example.com"       # OIDC 发行者标识 (通常为本系统 URL)
  jwks:
    private_key: "path/to/private.key"   # 用于签发 JWT 的私钥
    public_key: "path/to/public.pem"     # 公钥 (供客户端验证)
  access_token_exp: 3600                 # Access Token 有效期 (秒)
  id_token_exp: 3600                     # ID Token 有效期 (秒)

database:
  url: "sqlite:///data.db"               # 数据库连接 URL (支持 SQLite/PostgreSQL/MySQL)
启动流程和接口示例
服务启动：激活虚拟环境后，运行应用主脚本，如 python app.py 或使用 Flask 的 flask run 命令启动服务器。
SAML SP 工作流：
用户访问 SP 入口（如 /saml/login），系统构造 SAML AuthnRequest 并重定向至外部 SAML IdP。
用户在 IdP 上登录成功后，IdP 将包含用户身份信息的 SAML Response 提交到本系统的断言消费者服务端点（如 /saml/acs）。
系统校验 SAML 响应的签名并解析断言，从中提取用户属性，然后建立用户会话。
OIDC IdP 工作流：
OIDC 客户端浏览器重定向至授权端点，例如 GET /oauth/authorize?response_type=code&client_id=CLIENT_ID&redirect_uri=https://app/callback&scope=openid。
系统检查用户登录状态（若无会话则要求登录），并检查客户端及回调合法后，用户同意授权后，返回授权码到客户端回调地址。
客户端收到授权码后向令牌端点发送请求，例如：
bash
复制
编辑
POST /oauth/token
grant_type=authorization_code&code=<授权码>&redirect_uri=https://app/callback&client_id=CLIENT_ID&client_secret=SECRET
系统验证授权码后，颁发 access_token 和 id_token（JWT 格式）。
客户端可使用 id_token 中的信息或调用 /oauth/userinfo 端点获取当前用户的信息。
接口示例：
http
复制
编辑
GET /oauth/authorize?response_type=code&client_id=abc&redirect_uri=https://app/callback&scope=openid HTTP/1.1
Host: sp.example.com
http
复制
编辑
POST /oauth/token HTTP/1.1
Host: sp.example.com
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&code=<授权码>&redirect_uri=https://app/callback&client_id=abc&client_secret=xyz
响应示例（JSON）：
json
复制
编辑
{
  "access_token": "eyJhbGci...加密内容...",
  "id_token": "eyJ0eXAi...加密内容...",
  "token_type": "Bearer",
  "expires_in": 3600
}
安全机制说明
会话管理：用户在通过 SAML 或 OIDC 登录后，本系统在服务器端建立会话（例如使用 Flask-Login）。会话应使用安全 Cookie，并设置 HttpOnly、Secure 标志避免被脚本访问。会话应有超时机制和防止 CSRF 攻击的保护（例如在用户授权表单中加入 CSRF Token）。
SAML 安全性：系统配置了 IdP 的公钥证书，用于验证接收到的 SAML 响应签名，确保断言来源可信并未被篡改。可以要求 IdP 对 SAML Response 和 Assertion 都进行签名，提高安全性。所有 SAML 通信应通过 HTTPS 进行，防止中间人攻击。
OIDC 安全性：使用 JWT 格式的 id_token 并采用非对称签名（推荐使用 RSA 签名算法，如 RS256）进行签名
docs.authlib.org
。私钥仅在服务器端存储，客户端通过服务端公开的 JWK(s) 或证书验证签名。授权过程中使用 state 参数防止 CSRF，使用 nonce 防止重放攻击。Token 端点需通过 HTTPS 访问，且 client_secret、Tokens 等敏感数据不在日志中明文记录。
签名算法：一般选择 RS256 算法签名 ID Token，以避免共享对称密钥的风险
docs.authlib.org
。对于 SAML，可使用 xmlsec1 工具进行断言签名/验证。
扩展建议
多租户与多 IdP 支持：支持配置多个 SAML IdP，针对不同租户动态加载不同的 SAML 元数据。可将 IdP 配置存储在数据库，并实现多源元数据加载机制。
OpenID 配置发现：实现 OIDC .well-known/openid-configuration 接口，支持自动发现。
动态客户端注册：增加 OAuth2 动态客户端注册端点，使第三方客户端可以动态注册。
端点丰富化：实现 OAuth2 Token 失效（token revocation）、OAuth2 授权注销（logout）等端点，提升兼容性。
多因素认证：在用户登录环节加入 MFA（多因素认证）支持，如短信验证码或 TOTP，实现更高安全性。
用户界面/管理后台：提供管理界面用于查看和管理注册的客户端、查看用户会话、配置 SAML SP 参数等。
容器化部署：使用 Docker 或 Kubernetes 部署，方便规模化和运维管理。
pgsql
复制
编辑
